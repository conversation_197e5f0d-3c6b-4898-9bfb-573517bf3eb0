Starting single TTS task 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8 for user 0723
[WEBSOCKET-MANAGER] Starting TTS processing for taskId: 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8
[WEBSOCKET-MANAGER] Token available: YES
[WEBSOCKET-MANAGER] Token length: 178
[WEBSOCKET-MANAGER] Username: 0723
[INTERNAL-PROGRESS] 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8: 任务初始化...
Executed query {
  text: 'SELECT vip_info, usage_stats FROM users WHERE username = $1',
  duration: 29,
  rows: 1
}
[QUOTA-CHECK] User 0723 is under new quota rule. Checking quota...
Executed query {
  text: 'SELECT voice_id FROM voice_mappings WHERE voice_name = $1',
  duration: 1,
  rows: 0
}
[INTERNAL-PROGRESS] 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8: 文本分割中...
[INTERNAL-PROGRESS] 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8: 文本已分割为 2 个片段
[INTERNAL-PROGRESS] 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8: 正在生成 2 个音频片段...
[WORKER-POOL] Processing 2 chunks with 2 worker pool concurrency
Processing chunk 1/2, length: 982
[INFO] [2025-07-25T02:31:43.618Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-25T02:31:43.620Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":982,"voiceId":"pNInz6obpgDQGcFmaJgB","modelId":"eleven_multilingual_v2"}
[INFO] [2025-07-25T02:31:43.620Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","payloadSize":1146}
[INFO] [2025-07-25T02:31:43.621Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Initializing NetworkManager...
[DEBUG] [2025-07-25T02:31:43.621Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Creating network client for mode: gateway
[INFO] [2025-07-25T02:31:43.626Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing ProxyGateway...
[DEBUG] [2025-07-25T02:31:43.627Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing WorkerPoolController...
[INFO] [2025-07-25T02:31:43.627Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker pool initialized {"poolSize":10,"portRange":"1081-1090","selectorPrefix":"worker-selector"}
[INFO] [2025-07-25T02:31:43.627Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Initializing WorkerPoolController with Clash API...     
[DEBUG] [2025-07-25T02:31:43.628Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loading nodes from sing-box Clash API...
Processing chunk 2/2, length: 285
[INFO] [2025-07-25T02:31:43.647Z] [user:system] [task:N/A] - [TTS-ROUTE] Route decision: gateway {"decision":"gateway","networkMode":"gateway","gatewayEnabled":true}
[INFO] [2025-07-25T02:31:43.648Z] [user:system] [task:N/A] - [TTS-GATEWAY] Starting gateway request {"action":"Starting gateway request","textLength":285,"voiceId":"pNInz6obpgDQGcFmaJgB","modelId":"eleven_multilingual_v2"}
[INFO] [2025-07-25T02:31:43.648Z] [user:system] [task:N/A] - [TTS-GATEWAY] Sending request via network manager {"action":"Sending request via network manager","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","payloadSize":421}
[INFO] [2025-07-25T02:31:43.658Z] [user:system] [task:N/A] - [TTS-NODE] Loaded nodes for worker pool {"action":"Loaded nodes for worker pool","totalNodes":36,"validNodes":36,"nodeList":["sg-sg-013x-idx-0","sg-sg-023x-idx-1","sg-sg-033x-idx-2","sg-sg-043x-idx-3","sg-sg-053x-idx-4","sg-sg-063x-idx-5","sg-sg-073x-idx-6","sg-sg-083x-idx-7","sg-sg-093x-idx-8","jp-jp-013x-idx-9","...26 more"],"validNodeDetails":[{"name":"sg-sg-013x-idx-0","type":"Shadowsocks"},{"name":"sg-sg-023x-idx-1","type":"Shadowsocks"},{"name":"sg-sg-033x-idx-2","type":"Shadowsocks"},{"name":"sg-sg-043x-idx-3","type":"Trojan"},{"name":"sg-sg-053x-idx-4","type":"Trojan"},{"name":"sg-sg-063x-idx-5","type":"Trojan"},{"name":"sg-sg-073x-idx-6","type":"Trojan"},{"name":"sg-sg-083x-idx-7","type":"Trojan"},{"name":"sg-sg-093x-idx-8","type":"Trojan"},{"name":"jp-jp-013x-idx-9","type":"Shadowsocks"},{"name":"...26 more nodes","type":"truncated"}]}
[INFO] [2025-07-25T02:31:43.658Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loaded nodes from sing-box Clash API {"totalNodes":36,"validNodes":36,"nodeList":["sg-sg-013x-idx-0","sg-sg-023x-idx-1","sg-sg-033x-idx-2","sg-sg-043x-idx-3","sg-sg-053x-idx-4","sg-sg-063x-idx-5","sg-sg-073x-idx-6","sg-sg-083x-idx-7","sg-sg-093x-idx-8","jp-jp-013x-idx-9","...26 more"]}
[INFO] [2025-07-25T02:31:43.660Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Loaded quarantine data for 4 nodes
[DEBUG] [2025-07-25T02:31:43.660Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Validating worker selectors...
[INFO] [2025-07-25T02:31:43.673Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] All worker selectors validated successfully
[INFO] [2025-07-25T02:31:43.673Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] WorkerPoolController initialized successfully {"totalNodes":36,"healthyNodes":36,"quarantinedNodes":0,"totalWorkers":10,"apiType":"clash"}
[DEBUG] [2025-07-25T02:31:43.673Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] WorkerPoolController created and initialized (singleton)
[DEBUG] [2025-07-25T02:31:43.673Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Initializing NetworkAdapter...
[DEBUG] [2025-07-25T02:31:43.673Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] NetworkAdapter initialized
[INFO] [2025-07-25T02:31:43.674Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Starting health check with 30000ms interval
[INFO] [2025-07-25T02:31:43.674Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Starting quarantine check with 600000ms interval
[INFO] [2025-07-25T02:31:43.674Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] ProxyGateway initialized successfully {"mode":"gateway","singboxEnabled":true,"healthCheckEnabled":true}
[DEBUG] [2025-07-25T02:31:43.674Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Network client created for mode: gateway
[INFO] [2025-07-25T02:31:43.674Z] [user:system] [task:N/A] - [NETWORK-MANAGER] NetworkManager initialized successfully {"mode":"gateway","gatewayEnabled":true}
[DEBUG] [2025-07-25T02:31:43.674Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-25T02:31:43.675Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-25T02:31:43.675Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-013x-idx-0 (attempt 1/36)
[DEBUG] [2025-07-25T02:31:43.683Z] [user:system] [task:N/A] - [NETWORK-MANAGER] Making network request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-25T02:31:43.683Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","mode":"gateway"}
[DEBUG] [2025-07-25T02:31:43.684Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-023x-idx-1 (attempt 1/36)
[DEBUG] [2025-07-25T02:31:43.824Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-023x-idx-1 {"nodeTag":"sg-sg-023x-idx-1","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-25T02:31:43.824Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-023x-idx-1 passed health check 
[DEBUG] [2025-07-25T02:31:43.825Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-3 to switch to node: sg-sg-023x-idx-1
[INFO] [2025-07-25T02:31:43.825Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-3","targetNode":"sg-sg-023x-idx-1","originalNodeTag":"sg-sg-023x-idx-1","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-3","requestBody":{"name":"sg-sg-023x-idx-1"}}
[DEBUG] [2025-07-25T02:31:43.828Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-013x-idx-0 {"nodeTag":"sg-sg-013x-idx-0","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-25T02:31:43.828Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-013x-idx-0 passed health check 
[DEBUG] [2025-07-25T02:31:43.828Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-1 to switch to node: sg-sg-013x-idx-0
[INFO] [2025-07-25T02:31:43.828Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-1","targetNode":"sg-sg-013x-idx-0","originalNodeTag":"sg-sg-013x-idx-0","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-1","requestBody":{"name":"sg-sg-013x-idx-0"}}
[DEBUG] [2025-07-25T02:31:43.829Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-3     
[INFO] [2025-07-25T02:31:43.829Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-3","newNode":"sg-sg-023x-idx-1","fixedNodeTag":"sg-sg-023x-idx-1"}
[DEBUG] [2025-07-25T02:31:43.830Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-3 successfully switched to node: sg-sg-023x-idx-1
[DEBUG] [2025-07-25T02:31:43.830Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":3,"port":1083,"selector":"worker-selector-3","assignedNode":"sg-sg-023x-idx-1"}
[INFO] [2025-07-25T02:31:43.830Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":3,"workerPort":1083,"assignedNode":"sg-sg-023x-idx-1","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1"}
[INFO] [2025-07-25T02:31:43.953Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1083","workerId":3,"assignedNode":"sg-sg-023x-idx-1","method":"POST","hasProxy":true,"proxyType":"dispatcher"}
[DEBUG] [2025-07-25T02:31:43.957Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-1     
[INFO] [2025-07-25T02:31:43.957Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-1","newNode":"sg-sg-013x-idx-0","fixedNodeTag":"sg-sg-013x-idx-0"}
[DEBUG] [2025-07-25T02:31:43.958Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-1 successfully switched to node: sg-sg-013x-idx-0
[DEBUG] [2025-07-25T02:31:43.958Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":1,"port":1081,"selector":"worker-selector-1","assignedNode":"sg-sg-013x-idx-0"}
[INFO] [2025-07-25T02:31:43.958Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":1,"workerPort":1081,"assignedNode":"sg-sg-013x-idx-0","method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1"}
[INFO] [2025-07-25T02:31:43.958Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1081","workerId":1,"assignedNode":"sg-sg-013x-idx-0","method":"POST","hasProxy":true,"proxyType":"dispatcher"}
[INFO] [2025-07-25T02:31:47.426Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":3,"assignedNode":"sg-sg-023x-idx-1"}
[DEBUG] [2025-07-25T02:31:47.426Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":3,"port":1083,"currentNode":"sg-sg-023x-idx-1"}
[INFO] [2025-07-25T02:31:47.426Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":3,"assignedNode":"sg-sg-023x-idx-1"}
[DEBUG] [2025-07-25T02:31:47.427Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: POST https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","status":200,"duration":"3744ms"}
[INFO] [2025-07-25T02:31:47.427Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB","status":200,"duration":"3779ms","responseOk":true}
[INFO] [2025-07-25T02:31:47.613Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":315604,"totalDuration":"3965ms","requestDuration":"3779ms"}
[INFO] [2025-07-25T02:31:47.614Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":315604,"duration":"3967ms"} 
[DEBUG] [2025-07-25T02:31:48.675Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] Starting quarantine pool check...
[DEBUG] [2025-07-25T02:31:48.676Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] [QUARANTINE] No nodes in quarantine pool
[INFO] [2025-07-25T02:31:57.044Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":1,"assignedNode":"sg-sg-013x-idx-0"}
[DEBUG] [2025-07-25T02:31:57.045Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":1,"port":1081,"currentNode":"sg-sg-013x-idx-0"}
[INFO] [2025-07-25T02:31:57.045Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":1,"assignedNode":"sg-sg-013x-idx-0"}
[WARN] [2025-07-25T02:31:57.045Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Slow network request: POST https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1 {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB?allow_unauthenticated=1","status":200,"duration":"13370ms"}
[INFO] [2025-07-25T02:31:57.045Z] [user:system] [task:N/A] - [TTS-NETWORK] POST https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB {"method":"POST","url":"https://api.elevenlabs.io/v1/text-to-speech/pNInz6obpgDQGcFmaJgB","status":200,"duration":"13425ms","responseOk":true}
[INFO] [2025-07-25T02:31:57.750Z] [user:system] [task:N/A] - [TTS-GATEWAY] Request completed successfully {"action":"Request completed successfully","audioSize":1152358,"totalDuration":"14131ms","requestDuration":"13425ms"}
[INFO] [2025-07-25T02:31:57.751Z] [user:system] [task:N/A] - [TTS-SUCCESS] Generated via gateway {"mode":"gateway","audioSize":1152358,"duration":"14134ms"}
[INTERNAL-PROGRESS] 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8: 正在合并音频...
[INTERNAL-PROGRESS] 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8: 正在保存音频文件...
Audio file stored: \var\data\tts-app\audios\2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8.mp3, size: 1467962 bytes
Updated usage for user 0723: +1269 chars
[TTS-PROCESSOR] Generating secure URLs for taskId: 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8
[TTS-PROCESSOR] Token received: YES
[TTS-PROCESSOR] Token length: 178
[TTS-PROCESSOR] Generated secure streamUrl: http://localhost:3001/api/tts/stream/2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8
[TTS-PROCESSOR] Generated secure downloadUrl: http://localhost:3001/api/tts/download/2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8
[TTS-PROCESSOR] Token will be passed via Authorization header for security
[WEBSOCKET-MANAGER] Task 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8 finished with type: complete, scheduling connection close
[WEBSOCKET-MANAGER] Closing connection for task 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8, code: 1000, reason: Task finished: complete
[WEBSOCKET-MANAGER] Cleaned up connection for task 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8
WebSocket closed for task 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8
[STREAM] Request for taskId: 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8, token source: header
[STREAM] Token verified for user: 0723, taskId: 2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8
[STREAM] Checking file path: \var\data\tts-app\audios\2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8.mp3
[STREAM] File found, size: 1467962 bytes
[INFO] [2025-07-25T02:31:58.773Z] [user:system] [task:N/A] - GET /stream/2065b8f7-232c-4aa8-bcae-4b4c9c05c7c8 {"status":200,"duration":"9ms","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********","ip":"::1"}
[DEBUG] [2025-07-25T02:32:13.684Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-25T02:32:13.685Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-033x-idx-2 (attempt 1/36)
[DEBUG] [2025-07-25T02:32:13.813Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-033x-idx-2 {"nodeTag":"sg-sg-033x-idx-2","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-25T02:32:13.814Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-033x-idx-2 passed health check 
[DEBUG] [2025-07-25T02:32:13.814Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-3 to switch to node: sg-sg-033x-idx-2
[INFO] [2025-07-25T02:32:13.814Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-3","targetNode":"sg-sg-033x-idx-2","originalNodeTag":"sg-sg-033x-idx-2","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-3","requestBody":{"name":"sg-sg-033x-idx-2"}}
[DEBUG] [2025-07-25T02:32:13.815Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-3     
[INFO] [2025-07-25T02:32:13.816Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-3","newNode":"sg-sg-033x-idx-2","fixedNodeTag":"sg-sg-033x-idx-2"}
[DEBUG] [2025-07-25T02:32:13.816Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-3 successfully switched to node: sg-sg-033x-idx-2
[DEBUG] [2025-07-25T02:32:13.816Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":3,"port":1083,"selector":"worker-selector-3","assignedNode":"sg-sg-033x-idx-2"}
[INFO] [2025-07-25T02:32:13.816Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":3,"workerPort":1083,"assignedNode":"sg-sg-033x-idx-2","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T02:32:13.816Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1083","workerId":3,"assignedNode":"sg-sg-033x-idx-2","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[INFO] [2025-07-25T02:32:15.111Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker pool response received {"action":"Worker pool response received","status":200,"ok":true,"workerId":3,"assignedNode":"sg-sg-033x-idx-2"}
[DEBUG] [2025-07-25T02:32:15.111Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":3,"port":1083,"currentNode":"sg-sg-033x-idx-2"}
[INFO] [2025-07-25T02:32:15.111Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":3,"assignedNode":"sg-sg-033x-idx-2"}
[DEBUG] [2025-07-25T02:32:15.111Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Network request: GET https://httpbin.org/ip {"method":"GET","url":"https://httpbin.org/ip","status":200,"duration":"1427ms"}
[DEBUG] [2025-07-25T02:32:15.112Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check completed {"mode":"gateway","healthy":true,"status":200}
[DEBUG] [2025-07-25T02:32:15.112Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":true,"timestamp":1753410733684}
[DEBUG] [2025-07-25T02:32:43.698Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-25T02:32:43.699Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-043x-idx-3 (attempt 1/36)
[DEBUG] [2025-07-25T02:32:43.829Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-043x-idx-3 {"nodeTag":"sg-sg-043x-idx-3","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-25T02:32:43.829Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-043x-idx-3 passed health check 
[DEBUG] [2025-07-25T02:32:43.830Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-4 to switch to node: sg-sg-043x-idx-3
[INFO] [2025-07-25T02:32:43.830Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-4","targetNode":"sg-sg-043x-idx-3","originalNodeTag":"sg-sg-043x-idx-3","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-4","requestBody":{"name":"sg-sg-043x-idx-3"}}
[DEBUG] [2025-07-25T02:32:43.831Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-4     
[INFO] [2025-07-25T02:32:43.831Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-4","newNode":"sg-sg-043x-idx-3","fixedNodeTag":"sg-sg-043x-idx-3"}
[DEBUG] [2025-07-25T02:32:43.832Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-4 successfully switched to node: sg-sg-043x-idx-3
[DEBUG] [2025-07-25T02:32:43.832Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":4,"port":1084,"selector":"worker-selector-4","assignedNode":"sg-sg-043x-idx-3"}
[INFO] [2025-07-25T02:32:43.832Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":4,"workerPort":1084,"assignedNode":"sg-sg-043x-idx-3","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T02:32:43.832Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1084","workerId":4,"assignedNode":"sg-sg-043x-idx-3","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[ERROR] [2025-07-25T02:32:43.911Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":4,"assignedNode":"sg-sg-043x-idx-3"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":4,\"assignedNode\":\"sg-sg-043x-idx-3\"}","method":"GET","url":"https://httpbin.org/ip","workerId":4,"assignedNode":"sg-sg-043x-idx-3","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":4,\"assignedNode\":\"sg-sg-043x-idx-3\"}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:194:21)\n    at process.processTicksAndReject"}
[WARN] [2025-07-25T02:32:43.912Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":4,"nodeTag":"sg-sg-043x-idx-3"}
[WARN] [2025-07-25T02:32:43.912Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: sg-sg-043x-idx-3 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-25T02:32:43.912Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"sg-sg-043x-idx-3","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":35,"totalQuarantinedNodes":1}
[DEBUG] [2025-07-25T02:32:43.913Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":4,"port":1084,"currentNode":"sg-sg-043x-idx-3"}
[INFO] [2025-07-25T02:32:43.913Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":4,"assignedNode":"sg-sg-043x-idx-3"}
[ERROR] [2025-07-25T02:32:43.913Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T02:32:43.913Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"fetch failed"}
[WARN] [2025-07-25T02:32:43.913Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[DEBUG] [2025-07-25T02:32:43.913Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":false,"timestamp":1753410763698}
[DEBUG] [2025-07-25T02:32:43.914Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 1 nodes
[DEBUG] [2025-07-25T02:33:13.712Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Making gateway request {"method":"GET","url":"https://httpbin.org/ip","mode":"gateway"}
[DEBUG] [2025-07-25T02:33:13.713Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Testing node sg-sg-063x-idx-5 (attempt 1/35)
[DEBUG] [2025-07-25T02:33:13.842Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Health check for node sg-sg-063x-idx-5 {"nodeTag":"sg-sg-063x-idx-5","status":204,"healthy":true,"duration":5000}
[DEBUG] [2025-07-25T02:33:13.842Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] [LAZY CHECK] Node sg-sg-063x-idx-5 passed health check 
[DEBUG] [2025-07-25T02:33:13.843Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Commanding worker selector worker-selector-5 to switch to node: sg-sg-063x-idx-5
[INFO] [2025-07-25T02:33:13.843Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch command {"action":"Worker node switch command","selector":"worker-selector-5","targetNode":"sg-sg-063x-idx-5","originalNodeTag":"sg-sg-063x-idx-5","apiType":"clash","endpoint":"http://127.0.0.1:9090/proxies/worker-selector-5","requestBody":{"name":"sg-sg-063x-idx-5"}}
[DEBUG] [2025-07-25T02:33:13.844Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Empty response from PUT /proxies/worker-selector-5     
[INFO] [2025-07-25T02:33:13.845Z] [user:system] [task:N/A] - [TTS-NODE] Worker node switch successful {"action":"Worker node switch successful","selector":"worker-selector-5","newNode":"sg-sg-063x-idx-5","fixedNodeTag":"sg-sg-063x-idx-5"}
[DEBUG] [2025-07-25T02:33:13.845Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker selector worker-selector-5 successfully switched to node: sg-sg-063x-idx-5
[DEBUG] [2025-07-25T02:33:13.845Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker acquired with lazy health check {"workerId":5,"port":1085,"selector":"worker-selector-5","assignedNode":"sg-sg-063x-idx-5"}
[INFO] [2025-07-25T02:33:13.845Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker acquired for request {"action":"Worker acquired for request","workerId":5,"workerPort":1085,"assignedNode":"sg-sg-063x-idx-5","method":"GET","url":"https://httpbin.org/ip"}
[INFO] [2025-07-25T02:33:13.845Z] [user:system] [task:N/A] - [TTS-GATEWAY] Making request via worker pool {"action":"Making request via worker pool","socksProxy":"127.0.0.1:1085","workerId":5,"assignedNode":"sg-sg-063x-idx-5","method":"GET","hasProxy":true,"proxyType":"dispatcher"}
[ERROR] [2025-07-25T02:33:13.881Z] [user:system] [task:N/A] - [TTS-ERROR] Failed in worker-pool-request mode {"mode":"worker-pool-request","error":"fetch failed","method":"GET","url":"https://httpbin.org/ip","workerId":5,"assignedNode":"sg-sg-063x-idx-5"} {"mode":"worker-pool-request","error":"[TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":5,\"assignedNode\":\"sg-sg-063x-idx-5\"}","method":"GET","url":"https://httpbin.org/ip","workerId":5,"assignedNode":"sg-sg-063x-idx-5","stack":"Error: [TTS-ERROR] Failed in worker-pool-request mode {\"mode\":\"worker-pool-request\",\"error\":\"fetch failed\",\"method\":\"GET\",\"url\":\"https://httpbin.org/ip\",\"workerId\":5,\"assignedNode\":\"sg-sg-063x-idx-5\"}\n    at TTSRouteLogger.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:270:18)\n    at Object.logError (D:\\myaitts\\backend\\src\\utils\\ttsLogger.js:303:48)\n    at NetworkAdapter.requestViaGateway (D:\\myaitts\\backend\\src\\gateway\\adapters\\NetworkAdapter.js:194:21)\n    at process.processTicksAndReject"}
[WARN] [2025-07-25T02:33:13.881Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Worker pool network connectivity error {"error":"fetch failed","url":"https://httpbin.org/ip","workerId":5,"nodeTag":"sg-sg-063x-idx-5"}
[WARN] [2025-07-25T02:33:13.882Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Moving failed node to quarantine: sg-sg-063x-idx-5 {"reason":"Network error: fetch failed"}
[INFO] [2025-07-25T02:33:13.882Z] [user:system] [task:N/A] - [TTS-NODE] Node moved to quarantine {"action":"Node moved to quarantine","nodeTag":"sg-sg-063x-idx-5","reason":"Network error: fetch failed","quarantineType":"temporary","remainingHealthyNodes":34,"totalQuarantinedNodes":2}
[DEBUG] [2025-07-25T02:33:13.882Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Worker released {"workerId":5,"port":1085,"currentNode":"sg-sg-063x-idx-5"}
[INFO] [2025-07-25T02:33:13.882Z] [user:system] [task:N/A] - [TTS-GATEWAY] Worker released {"action":"Worker released","workerId":5,"assignedNode":"sg-sg-063x-idx-5"}
[ERROR] [2025-07-25T02:33:13.882Z] [user:system] [task:N/A] - Unknown error {"error":"Unknown error"}
[WARN] [2025-07-25T02:33:13.883Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:NETWORK] Health check failed {"error":"fetch failed"}
[WARN] [2025-07-25T02:33:13.883Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Network adapter health check failed
[DEBUG] [2025-07-25T02:33:13.883Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY] Health check completed {"networkHealthy":false,"timestamp":1753410793712}
[DEBUG] [2025-07-25T02:33:13.884Z] [user:system] [task:N/A] - [NETWORK-MANAGER:GATEWAY:WORKER-POOL] Saved quarantine data for 2 nodes
[INFO] [2025-07-25T02:33:20.512Z] [user:system] [task:N/A] - SIGINT received, shutting down gracefully
[WEBSOCKET-MANAGER] Cleanup timer stopped
[INFO] [2025-07-25T02:33:20.513Z] [user:system] [task:N/A] - All connections closed successfully