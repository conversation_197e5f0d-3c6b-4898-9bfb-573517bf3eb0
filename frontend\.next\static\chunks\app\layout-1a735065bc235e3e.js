(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{2284:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(2115),o=r(6081),s=r(6101),a=r(9708),i=r(5155);function l(e){let t=e+"CollectionProvider",[r,l]=(0,o.A)(t),[d,u]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:r}=e,o=n.useRef(null),s=n.useRef(new Map).current;return(0,i.jsx)(d,{scope:t,itemMap:s,collectionRef:o,children:r})};c.displayName=t;let f=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=u(f,r),l=(0,s.s)(t,o.collectionRef);return(0,i.jsx)(a.DX,{ref:l,children:n})});p.displayName=f;let v=e+"CollectionItemSlot",m="data-radix-collection-item",w=n.forwardRef((e,t)=>{let{scope:r,children:o,...l}=e,d=n.useRef(null),c=(0,s.s)(t,d),f=u(v,r);return n.useEffect(()=>(f.itemMap.set(d,{ref:d,...l}),()=>void f.itemMap.delete(d))),(0,i.jsx)(a.DX,{[m]:"",ref:c,children:o})});return w.displayName=v,[{Provider:c,Slot:p,ItemSlot:w},function(t){let r=u(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}},3580:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f});var n=r(2115);let o=0,s=new Map,a=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function u(e){d=i(d,e),l.forEach(e=>{e(d)})}function c(e){let{...t}=e,r=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>u({type:"DISMISS_TOAST",toastId:r});return u({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(d);return n.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},3999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(2596),o=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,n.$)(t))}},7216:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>ea});var n=r(5155),o=r(3580),s=r(2115),a=r(7650),i=r(5185),l=r(6101),d=r(2284),u=r(6081),c=r(9178),f=r(4378),p=r(8905),v=r(3655),m=r(9033),w=r(5845),x=r(2712),y=s.forwardRef((e,t)=>(0,n.jsx)(v.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));y.displayName="VisuallyHidden";var g="ToastProvider",[h,E,T]=(0,d.N)("Toast"),[b,R]=(0,u.A)("Toast",[T]),[N,j]=b(g),C=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:l}=e,[d,u]=s.useState(null),[c,f]=s.useState(0),p=s.useRef(!1),v=s.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(g,"`. Expected non-empty `string`.")),(0,n.jsx)(h.Provider,{scope:t,children:(0,n.jsx)(N,{scope:t,label:r,duration:o,swipeDirection:a,swipeThreshold:i,toastCount:c,viewport:d,onViewportChange:u,onToastAdd:s.useCallback(()=>f(e=>e+1),[]),onToastRemove:s.useCallback(()=>f(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:v,children:l})})};C.displayName=g;var S="ToastViewport",P=["F8"],A="toast.viewportPause",D="toast.viewportResume",I=s.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=P,label:a="Notifications ({hotkey})",...i}=e,d=j(S,r),u=E(r),f=s.useRef(null),p=s.useRef(null),m=s.useRef(null),w=s.useRef(null),x=(0,l.s)(t,w,d.onViewportChange),y=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),g=d.toastCount>0;s.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null===(t=w.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),s.useEffect(()=>{let e=f.current,t=w.current;if(g&&e&&t){let r=()=>{if(!d.isClosePausedRef.current){let e=new CustomEvent(A);t.dispatchEvent(e),d.isClosePausedRef.current=!0}},n=()=>{if(d.isClosePausedRef.current){let e=new CustomEvent(D);t.dispatchEvent(e),d.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},s=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",s),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",s),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[g,d.isClosePausedRef]);let T=s.useCallback(e=>{let{tabbingDirection:t}=e,r=u().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[u]);return s.useEffect(()=>{let e=w.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,s;let r=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null===(n=p.current)||void 0===n||n.focus();return}let i=T({tabbingDirection:a?"backwards":"forwards"}),l=i.findIndex(e=>e===r);Z(i.slice(l+1))?t.preventDefault():a?null===(o=p.current)||void 0===o||o.focus():null===(s=m.current)||void 0===s||s.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[u,T]),(0,n.jsxs)(c.lg,{ref:f,role:"region","aria-label":a.replace("{hotkey}",y),tabIndex:-1,style:{pointerEvents:g?void 0:"none"},children:[g&&(0,n.jsx)(M,{ref:p,onFocusFromOutsideViewport:()=>{Z(T({tabbingDirection:"forwards"}))}}),(0,n.jsx)(h.Slot,{scope:r,children:(0,n.jsx)(v.sG.ol,{tabIndex:-1,...i,ref:x})}),g&&(0,n.jsx)(M,{ref:m,onFocusFromOutsideViewport:()=>{Z(T({tabbingDirection:"backwards"}))}})]})});I.displayName=S;var L="ToastFocusProxy",M=s.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:o,...s}=e,a=j(L,r);return(0,n.jsx)(y,{"aria-hidden":!0,tabIndex:0,...s,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=a.viewport)||void 0===t?void 0:t.contains(r))||o()}})});M.displayName=L;var k="Toast",F=s.forwardRef((e,t)=>{let{forceMount:r,open:o,defaultOpen:s,onOpenChange:a,...l}=e,[d=!0,u]=(0,w.i)({prop:o,defaultProp:s,onChange:a});return(0,n.jsx)(p.C,{present:r||d,children:(0,n.jsx)(K,{open:d,...l,ref:t,onClose:()=>u(!1),onPause:(0,m.c)(e.onPause),onResume:(0,m.c)(e.onResume),onSwipeStart:(0,i.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,i.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,i.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,i.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),u(!1)})})})});F.displayName=k;var[O,_]=b(k,{onClose(){}}),K=s.forwardRef((e,t)=>{let{__scopeToast:r,type:o="foreground",duration:d,open:u,onClose:f,onEscapeKeyDown:p,onPause:w,onResume:x,onSwipeStart:y,onSwipeMove:g,onSwipeCancel:E,onSwipeEnd:T,...b}=e,R=j(k,r),[N,C]=s.useState(null),S=(0,l.s)(t,e=>C(e)),P=s.useRef(null),I=s.useRef(null),L=d||R.duration,M=s.useRef(0),F=s.useRef(L),_=s.useRef(0),{onToastAdd:K,onToastRemove:G}=R,X=(0,m.c)(()=>{var e;(null==N?void 0:N.contains(document.activeElement))&&(null===(e=R.viewport)||void 0===e||e.focus()),f()}),U=s.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(_.current),M.current=new Date().getTime(),_.current=window.setTimeout(X,e))},[X]);s.useEffect(()=>{let e=R.viewport;if(e){let t=()=>{U(F.current),null==x||x()},r=()=>{let e=new Date().getTime()-M.current;F.current=F.current-e,window.clearTimeout(_.current),null==w||w()};return e.addEventListener(A,r),e.addEventListener(D,t),()=>{e.removeEventListener(A,r),e.removeEventListener(D,t)}}},[R.viewport,L,w,x,U]),s.useEffect(()=>{u&&!R.isClosePausedRef.current&&U(L)},[u,L,R.isClosePausedRef,U]),s.useEffect(()=>(K(),()=>G()),[K,G]);let q=s.useMemo(()=>N?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var n;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(n=t).nodeType===n.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(N):null,[N]);return R.viewport?(0,n.jsxs)(n.Fragment,{children:[q&&(0,n.jsx)(V,{__scopeToast:r,role:"status","aria-live":"foreground"===o?"assertive":"polite","aria-atomic":!0,children:q}),(0,n.jsx)(O,{scope:r,onClose:X,children:a.createPortal((0,n.jsx)(h.ItemSlot,{scope:r,children:(0,n.jsx)(c.bL,{asChild:!0,onEscapeKeyDown:(0,i.m)(p,()=>{R.isFocusedToastEscapeKeyDownRef.current||X(),R.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,n.jsx)(v.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":u?"open":"closed","data-swipe-direction":R.swipeDirection,...b,ref:S,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Escape"!==e.key||(null==p||p(e.nativeEvent),e.nativeEvent.defaultPrevented||(R.isFocusedToastEscapeKeyDownRef.current=!0,X()))}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{0===e.button&&(P.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{if(!P.current)return;let t=e.clientX-P.current.x,r=e.clientY-P.current.y,n=!!I.current,o=["left","right"].includes(R.swipeDirection),s=["left","up"].includes(R.swipeDirection)?Math.min:Math.max,a=o?s(0,t):0,i=o?0:s(0,r),l="touch"===e.pointerType?10:2,d={x:a,y:i},u={originalEvent:e,delta:d};n?(I.current=d,z("toast.swipeMove",g,u,{discrete:!1})):Q(d,R.swipeDirection,l)?(I.current=d,z("toast.swipeStart",y,u,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(P.current=null)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=I.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),I.current=null,P.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};Q(t,R.swipeDirection,R.swipeThreshold)?z("toast.swipeEnd",T,n,{discrete:!0}):z("toast.swipeCancel",E,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),R.viewport)})]}):null}),V=e=>{let{__scopeToast:t,children:r,...o}=e,a=j(k,t),[i,l]=s.useState(!1),[d,u]=s.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,m.c)(e);(0,x.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>l(!0)),s.useEffect(()=>{let e=window.setTimeout(()=>u(!0),1e3);return()=>window.clearTimeout(e)},[]),d?null:(0,n.jsx)(f.Z,{asChild:!0,children:(0,n.jsx)(y,{...o,children:i&&(0,n.jsxs)(n.Fragment,{children:[a.label," ",r]})})})},G=s.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e;return(0,n.jsx)(v.sG.div,{...o,ref:t})});G.displayName="ToastTitle";var X=s.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e;return(0,n.jsx)(v.sG.div,{...o,ref:t})});X.displayName="ToastDescription";var U="ToastAction",q=s.forwardRef((e,t)=>{let{altText:r,...o}=e;return r.trim()?(0,n.jsx)(Y,{altText:r,asChild:!0,children:(0,n.jsx)(W,{...o,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(U,"`. Expected non-empty `string`.")),null)});q.displayName=U;var H="ToastClose",W=s.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e,s=_(H,r);return(0,n.jsx)(Y,{asChild:!0,children:(0,n.jsx)(v.sG.button,{type:"button",...o,ref:t,onClick:(0,i.m)(e.onClick,s.onClose)})})});W.displayName=H;var Y=s.forwardRef((e,t)=>{let{__scopeToast:r,altText:o,...s}=e;return(0,n.jsx)(v.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":o||void 0,...s,ref:t})});function z(e,t,r,n){let{discrete:o}=n,s=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),o?(0,v.hO)(s,a):s.dispatchEvent(a)}var Q=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),s=n>o;return"left"===t||"right"===t?s&&n>r:!s&&o>r};function Z(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var $=r(2085),B=r(4416),J=r(3999);let ee=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(I,{ref:t,className:(0,J.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...o})});ee.displayName=I.displayName;let et=(0,$.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),er=s.forwardRef((e,t)=>{let{className:r,variant:o,...s}=e;return(0,n.jsx)(F,{ref:t,className:(0,J.cn)(et({variant:o}),r),...s})});er.displayName=F.displayName,s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(q,{ref:t,className:(0,J.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...o})}).displayName=q.displayName;let en=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(W,{ref:t,className:(0,J.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...o,children:(0,n.jsx)(B.A,{className:"h-4 w-4"})})});en.displayName=W.displayName;let eo=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(G,{ref:t,className:(0,J.cn)("text-sm font-semibold",r),...o})});eo.displayName=G.displayName;let es=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(X,{ref:t,className:(0,J.cn)("text-sm opacity-90",r),...o})});function ea(){let{toasts:e}=(0,o.dj)();return(0,n.jsxs)(C,{children:[e.map(function(e){let{id:t,title:r,description:o,action:s,...a}=e;return(0,n.jsxs)(er,{...a,children:[(0,n.jsxs)("div",{className:"grid gap-1",children:[r&&(0,n.jsx)(eo,{children:r}),o&&(0,n.jsx)(es,{children:o})]}),s,(0,n.jsx)(en,{})]},t)}),(0,n.jsx)(ee,{})]})}es.displayName=X.displayName},7328:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9324,23)),Promise.resolve().then(r.bind(r,7216))},9324:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[533,352,150,441,684,358],()=>t(7328)),_N_E=e.O()}]);