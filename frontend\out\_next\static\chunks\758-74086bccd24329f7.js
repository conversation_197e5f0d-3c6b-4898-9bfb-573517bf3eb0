"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[758],{1285:(e,t,n)=>{n.d(t,{B:()=>c});var r,o=n(2115),a=n(2712),i=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),u=0;function c(e){let[t,n]=o.useState(i());return(0,a.N)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},3651:(e,t,n)=>{n.d(t,{bm:()=>e7,UC:()=>e8,VY:()=>e3,hJ:()=>e2,ZL:()=>e5,bL:()=>e0,hE:()=>e6,l9:()=>e1});var r,o=n(2115),a=n(5185),i=n(6101),u=n(6081),c=n(1285),l=n(5845),s=n(9178),d=n(3655),f=n(9033),v=n(5155),p="focusScope.autoFocusOnMount",m="focusScope.autoFocusOnUnmount",h={bubbles:!1,cancelable:!0},g=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:a,onUnmountAutoFocus:u,...c}=e,[l,s]=o.useState(null),g=(0,f.c)(a),C=(0,f.c)(u),R=o.useRef(null),x=(0,i.s)(t,e=>s(e)),S=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(S.paused||!l)return;let t=e.target;l.contains(t)?R.current=t:E(R.current,{select:!0})},t=function(e){if(S.paused||!l)return;let t=e.relatedTarget;null===t||l.contains(t)||E(R.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&E(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,S.paused]),o.useEffect(()=>{if(l){w.add(S);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(p,h);l.addEventListener(p,g),l.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(E(r,{select:t}),document.activeElement!==n)return}(y(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&E(l))}return()=>{l.removeEventListener(p,g),setTimeout(()=>{let t=new CustomEvent(m,h);l.addEventListener(m,C),l.dispatchEvent(t),t.defaultPrevented||E(null!=e?e:document.body,{select:!0}),l.removeEventListener(m,C),w.remove(S)},0)}}},[l,g,C,S]);let D=o.useCallback(e=>{if(!n&&!r||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=y(e);return[b(t,e),b(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&E(a,{select:!0})):(e.preventDefault(),n&&E(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,S.paused]);return(0,v.jsx)(d.sG.div,{tabIndex:-1,...c,ref:x,onKeyDown:D})});function y(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function b(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function E(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}g.displayName="FocusScope";var w=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=C(e,t)).unshift(t)},remove(t){var n;null===(n=(e=C(e,t))[0])||void 0===n||n.resume()}}}();function C(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var R=n(4378),x=n(8905),S=0;function D(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var N=function(){return(N=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function L(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var k=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),O="width-before-scroll-bar";function P(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var A="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,T=new WeakMap;function j(e){return e}var M=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=j),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return a.options=N({async:!0,ssr:!1},e),a}(),I=function(){},F=o.forwardRef(function(e,t){var n,r,a,i,u=o.useRef(null),c=o.useState({onScrollCapture:I,onWheelCapture:I,onTouchMoveCapture:I}),l=c[0],s=c[1],d=e.forwardProps,f=e.children,v=e.className,p=e.removeScrollBar,m=e.enabled,h=e.shards,g=e.sideCar,y=e.noRelative,b=e.noIsolation,E=e.inert,w=e.allowPinchZoom,C=e.as,R=e.gapMode,x=L(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(n=[u,t],r=function(e){return n.forEach(function(t){return P(t,e)})},(a=(0,o.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,i=a.facade,A(function(){var e=T.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||P(e,null)}),r.forEach(function(e){t.has(e)||P(e,o)})}T.set(i,n)},[n]),i),D=N(N({},x),l);return o.createElement(o.Fragment,null,m&&o.createElement(g,{sideCar:M,removeScrollBar:p,shards:h,noRelative:y,noIsolation:b,inert:E,setCallbacks:s,allowPinchZoom:!!w,lockRef:u,gapMode:R}),d?o.cloneElement(o.Children.only(f),N(N({},D),{ref:S})):o.createElement(void 0===C?"div":C,N({},D,{className:v,ref:S}),f))});F.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},F.classNames={fullWidth:O,zeroRight:k};var W=function(e){var t=e.sideCar,n=L(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return o.createElement(r,N({},n))};W.isSideCarExport=!0;var _=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},B=function(){var e=_();return function(t,n){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},G=function(){var e=B();return function(t){return e(t.styles,t.dynamic),null}},K={left:0,top:0,right:0,gap:0},X=function(e){return parseInt(e||"",10)||0},Y=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[X(n),X(r),X(o)]},Z=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return K;var t=Y(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},q=G(),z="data-scroll-locked",U=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(z,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(k," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(O," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(k," .").concat(k," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(O," .").concat(O," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(z,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},H=function(){var e=parseInt(document.body.getAttribute(z)||"0",10);return isFinite(e)?e:0},V=function(){o.useEffect(function(){return document.body.setAttribute(z,(H()+1).toString()),function(){var e=H()-1;e<=0?document.body.removeAttribute(z):document.body.setAttribute(z,e.toString())}},[])},J=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,a=void 0===r?"margin":r;V();var i=o.useMemo(function(){return Z(a)},[a]);return o.createElement(q,{styles:U(i,!t,a,n?"":"!important")})},$=!1;if("undefined"!=typeof window)try{var Q=Object.defineProperty({},"passive",{get:function(){return $=!0,!0}});window.addEventListener("test",Q,Q),window.removeEventListener("test",Q,Q)}catch(e){$=!1}var ee=!!$&&{passive:!1},et=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},en=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),er(e,r)){var o=eo(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},er=function(e,t){return"v"===e?et(t,"overflowY"):et(t,"overflowX")},eo=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ea=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),u=i*r,c=n.target,l=t.contains(c),s=!1,d=u>0,f=0,v=0;do{if(!c)break;var p=eo(e,c),m=p[0],h=p[1]-p[2]-i*m;(m||h)&&er(e,c)&&(f+=h,v+=m);var g=c.parentNode;c=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!l&&c!==document.body||l&&(t.contains(c)||t===c));return d&&(o&&1>Math.abs(f)||!o&&u>f)?s=!0:!d&&(o&&1>Math.abs(v)||!o&&-u>v)&&(s=!0),s},ei=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},eu=function(e){return[e.deltaX,e.deltaY]},ec=function(e){return e&&"current"in e?e.current:e},el=0,es=[];let ed=(M.useMedium(function(e){var t=o.useRef([]),n=o.useRef([0,0]),r=o.useRef(),a=o.useState(el++)[0],i=o.useState(G)[0],u=o.useRef(e);o.useEffect(function(){u.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(ec),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var c=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!u.current.allowPinchZoom;var o,a=ei(e),i=n.current,c="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(c)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=en(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=en(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(c||l)&&(r.current=o),!o)return!0;var v=r.current||o;return ea(v,t,e,"h"===v?c:l,!0)},[]),l=o.useCallback(function(e){if(es.length&&es[es.length-1]===i){var n="deltaY"in e?eu(e):ei(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(u.current.shards||[]).map(ec).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?c(e,o[0]):!u.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=o.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=o.useCallback(function(e){n.current=ei(e),r.current=void 0},[]),f=o.useCallback(function(t){s(t.type,eu(t),t.target,c(t,e.lockRef.current))},[]),v=o.useCallback(function(t){s(t.type,ei(t),t.target,c(t,e.lockRef.current))},[]);o.useEffect(function(){return es.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:v}),document.addEventListener("wheel",l,ee),document.addEventListener("touchmove",l,ee),document.addEventListener("touchstart",d,ee),function(){es=es.filter(function(e){return e!==i}),document.removeEventListener("wheel",l,ee),document.removeEventListener("touchmove",l,ee),document.removeEventListener("touchstart",d,ee)}},[]);var p=e.removeScrollBar,m=e.inert;return o.createElement(o.Fragment,null,m?o.createElement(i,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,p?o.createElement(J,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),W);var ef=o.forwardRef(function(e,t){return o.createElement(F,N({},e,{ref:t,sideCar:ed}))});ef.classNames=F.classNames;var ev=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},ep=new WeakMap,em=new WeakMap,eh={},eg=0,ey=function(e){return e&&(e.host||ey(e.parentNode))},eb=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=ey(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eh[n]||(eh[n]=new WeakMap);var a=eh[n],i=[],u=new Set,c=new Set(o),l=function(e){!(!e||u.has(e))&&(u.add(e),l(e.parentNode))};o.forEach(l);var s=function(e){!(!e||c.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(u.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,c=(ep.get(e)||0)+1,l=(a.get(e)||0)+1;ep.set(e,c),a.set(e,l),i.push(e),1===c&&o&&em.set(e,!0),1===l&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),u.clear(),eg++,function(){i.forEach(function(e){var t=ep.get(e)-1,o=a.get(e)-1;ep.set(e,t),a.set(e,o),t||(em.has(e)||e.removeAttribute(r),em.delete(e)),o||e.removeAttribute(n)}),--eg||(ep=new WeakMap,ep=new WeakMap,em=new WeakMap,eh={})}},eE=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||ev(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),eb(r,o,n,"aria-hidden")):function(){return null}},ew=n(9708),eC="Dialog",[eR,ex]=(0,u.A)(eC),[eS,eD]=eR(eC),eN=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:a,onOpenChange:i,modal:u=!0}=e,s=o.useRef(null),d=o.useRef(null),[f=!1,p]=(0,l.i)({prop:r,defaultProp:a,onChange:i});return(0,v.jsx)(eS,{scope:t,triggerRef:s,contentRef:d,contentId:(0,c.B)(),titleId:(0,c.B)(),descriptionId:(0,c.B)(),open:f,onOpenChange:p,onOpenToggle:o.useCallback(()=>p(e=>!e),[p]),modal:u,children:n})};eN.displayName=eC;var eL="DialogTrigger",ek=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eD(eL,n),u=(0,i.s)(t,o.triggerRef);return(0,v.jsx)(d.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":eU(o.open),...r,ref:u,onClick:(0,a.m)(e.onClick,o.onOpenToggle)})});ek.displayName=eL;var eO="DialogPortal",[eP,eA]=eR(eO,{forceMount:void 0}),eT=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:a}=e,i=eD(eO,t);return(0,v.jsx)(eP,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,v.jsx)(x.C,{present:n||i.open,children:(0,v.jsx)(R.Z,{asChild:!0,container:a,children:e})}))})};eT.displayName=eO;var ej="DialogOverlay",eM=o.forwardRef((e,t)=>{let n=eA(ej,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eD(ej,e.__scopeDialog);return a.modal?(0,v.jsx)(x.C,{present:r||a.open,children:(0,v.jsx)(eI,{...o,ref:t})}):null});eM.displayName=ej;var eI=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eD(ej,n);return(0,v.jsx)(ef,{as:ew.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,v.jsx)(d.sG.div,{"data-state":eU(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eF="DialogContent",eW=o.forwardRef((e,t)=>{let n=eA(eF,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eD(eF,e.__scopeDialog);return(0,v.jsx)(x.C,{present:r||a.open,children:a.modal?(0,v.jsx)(e_,{...o,ref:t}):(0,v.jsx)(eB,{...o,ref:t})})});eW.displayName=eF;var e_=o.forwardRef((e,t)=>{let n=eD(eF,e.__scopeDialog),r=o.useRef(null),u=(0,i.s)(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return eE(e)},[]),(0,v.jsx)(eG,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),eB=o.forwardRef((e,t)=>{let n=eD(eF,e.__scopeDialog),r=o.useRef(!1),a=o.useRef(!1);return(0,v.jsx)(eG,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,i;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:t=>{var o,i;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let u=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(u))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),eG=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:u,...c}=e,l=eD(eF,n),d=o.useRef(null),f=(0,i.s)(t,d);return o.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:D()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:D()),S++,()=>{1===S&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),S--}},[]),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(g,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:u,children:(0,v.jsx)(s.qW,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":eU(l.open),...c,ref:f,onDismiss:()=>l.onOpenChange(!1)})}),(0,v.jsxs)(v.Fragment,{children:[(0,v.jsx)(e$,{titleId:l.titleId}),(0,v.jsx)(eQ,{contentRef:d,descriptionId:l.descriptionId})]})]})}),eK="DialogTitle",eX=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eD(eK,n);return(0,v.jsx)(d.sG.h2,{id:o.titleId,...r,ref:t})});eX.displayName=eK;var eY="DialogDescription",eZ=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eD(eY,n);return(0,v.jsx)(d.sG.p,{id:o.descriptionId,...r,ref:t})});eZ.displayName=eY;var eq="DialogClose",ez=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eD(eq,n);return(0,v.jsx)(d.sG.button,{type:"button",...r,ref:t,onClick:(0,a.m)(e.onClick,()=>o.onOpenChange(!1))})});function eU(e){return e?"open":"closed"}ez.displayName=eq;var eH="DialogTitleWarning",[eV,eJ]=(0,u.q)(eH,{contentName:eF,titleName:eK,docsSlug:"dialog"}),e$=e=>{let{titleId:t}=e,n=eJ(eH),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return o.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},eQ=e=>{let{contentRef:t,descriptionId:n}=e,r=eJ("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return o.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(a)},[a,t,n]),null},e0=eN,e1=ek,e5=eT,e2=eM,e8=eW,e6=eX,e3=eZ,e7=ez},4378:(e,t,n)=>{n.d(t,{Z:()=>c});var r=n(2115),o=n(7650),a=n(3655),i=n(2712),u=n(5155),c=r.forwardRef((e,t)=>{var n,c;let{container:l,...s}=e,[d,f]=r.useState(!1);(0,i.N)(()=>f(!0),[]);let v=l||d&&(null===(c=globalThis)||void 0===c?void 0:null===(n=c.document)||void 0===n?void 0:n.body);return v?o.createPortal((0,u.jsx)(a.sG.div,{...s,ref:t}),v):null});c.displayName="Portal"},4416:(e,t,n)=>{n.d(t,{A:()=>r});let r=(0,n(9946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},9178:(e,t,n)=>{n.d(t,{lg:()=>g,qW:()=>f,bL:()=>h});var r,o=n(2115),a=n(5185),i=n(3655),u=n(6101),c=n(9033),l=n(5155),s="dismissableLayer.update",d=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var n,f;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:h,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:b,onDismiss:E,...w}=e,C=o.useContext(d),[R,x]=o.useState(null),S=null!==(f=null==R?void 0:R.ownerDocument)&&void 0!==f?f:null===(n=globalThis)||void 0===n?void 0:n.document,[,D]=o.useState({}),N=(0,u.s)(t,e=>x(e)),L=Array.from(C.layers),[k]=[...C.layersWithOutsidePointerEventsDisabled].slice(-1),O=L.indexOf(k),P=R?L.indexOf(R):-1,A=C.layersWithOutsidePointerEventsDisabled.size>0,T=P>=O,j=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,c.c)(e),a=o.useRef(!1),i=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!a.current){let t=function(){m("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",i.current),i.current=t,n.addEventListener("click",i.current,{once:!0})):t()}else n.removeEventListener("click",i.current);a.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",i.current)}},[n,r]),{onPointerDownCapture:()=>a.current=!0}}(e=>{let t=e.target,n=[...C.branches].some(e=>e.contains(t));!T||n||(null==g||g(e),null==b||b(e),e.defaultPrevented||null==E||E())},S),M=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,r=(0,c.c)(e),a=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!a.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>a.current=!0,onBlurCapture:()=>a.current=!1}}(e=>{let t=e.target;[...C.branches].some(e=>e.contains(t))||(null==y||y(e),null==b||b(e),e.defaultPrevented||null==E||E())},S);return!function(e,t=globalThis?.document){let n=(0,c.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{P===C.layers.size-1&&(null==h||h(e),!e.defaultPrevented&&E&&(e.preventDefault(),E()))},S),o.useEffect(()=>{if(R)return v&&(0===C.layersWithOutsidePointerEventsDisabled.size&&(r=S.body.style.pointerEvents,S.body.style.pointerEvents="none"),C.layersWithOutsidePointerEventsDisabled.add(R)),C.layers.add(R),p(),()=>{v&&1===C.layersWithOutsidePointerEventsDisabled.size&&(S.body.style.pointerEvents=r)}},[R,S,v,C]),o.useEffect(()=>()=>{R&&(C.layers.delete(R),C.layersWithOutsidePointerEventsDisabled.delete(R),p())},[R,C]),o.useEffect(()=>{let e=()=>D({});return document.addEventListener(s,e),()=>document.removeEventListener(s,e)},[]),(0,l.jsx)(i.sG.div,{...w,ref:N,style:{pointerEvents:A?T?"auto":"none":void 0,...e.style},onFocusCapture:(0,a.m)(e.onFocusCapture,M.onFocusCapture),onBlurCapture:(0,a.m)(e.onBlurCapture,M.onBlurCapture),onPointerDownCapture:(0,a.m)(e.onPointerDownCapture,j.onPointerDownCapture)})});f.displayName="DismissableLayer";var v=o.forwardRef((e,t)=>{let n=o.useContext(d),r=o.useRef(null),a=(0,u.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,l.jsx)(i.sG.div,{...e,ref:a})});function p(){let e=new CustomEvent(s);document.dispatchEvent(e)}function m(e,t,n,r){let{discrete:o}=r,a=n.originalEvent.target,u=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?(0,i.hO)(a,u):a.dispatchEvent(u)}v.displayName="DismissableLayerBranch";var h=f,g=v}}]);