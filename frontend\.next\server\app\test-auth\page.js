(()=>{var e={};e.id=580,e.ids=[580],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1028:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>l});var s=r(5239),i=r(8088),n=r(8170),o=r.n(n),a=r(893),d={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l={children:["",{children:["test-auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9255)),"D:\\myaitts\\frontend\\app\\test-auth\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,8014)),"D:\\myaitts\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\myaitts\\frontend\\app\\test-auth\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/test-auth/page",pathname:"/test-auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},1960:(e,t,r)=>{Promise.resolve().then(r.bind(r,9255))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5478:(e,t,r)=>{"use strict";r.d(t,{A:()=>o,tu:()=>n});let s={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"};function i(e){if(e?.code)return Object.values(s).includes(e.code);if(e?.message){let t=e.message.toLowerCase();return t.includes("token")||t.includes("expired")||t.includes("unauthorized")||t.includes("401")||t.includes("登录")||t.includes("refresh")}return!1}function n(e,t){let r=i(e);r&&t&&t();let s=i(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:e?.shouldRedirect===void 0||e.shouldRedirect}:{title:"操作失败",description:e?.message||"请检查网络连接后重试",shouldRedirect:!1};return{isAuthError:r,message:s.description,shouldRedirect:s.shouldRedirect}}function o(e){return i(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:e?.message?.includes("卡密")?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:e?.message||"请检查网络连接后重试",variant:"destructive"}}},5497:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(687),i=r(3210),n=r(4934),o=r(5192),a=r(4426),d=r(9556),l=r(1702),c=r(5478);function u(){let[e,t]=(0,i.useState)(""),[r,u]=(0,i.useState)(!1),{toast:p}=(0,l.dj)(),h=async()=>{try{d.tC.setTokens("invalid_token","invalid_refresh_token","<EMAIL>");let e=await a.j2.getUserQuota();t("成功获取数据: "+JSON.stringify(e))}catch(s){console.error("测试错误:",s),t("捕获错误: "+s.message+" (code: "+(s.code||"无")+")");let{isAuthError:e,shouldRedirect:r}=(0,c.tu)(s,()=>{u(!0),p({title:"认证失败",description:"会话已过期，正在跳转到登录页面...",variant:"destructive"})});e&&r&&setTimeout(()=>{window.location.href="/login"},2e3)}},m=async()=>{try{d.tC.clearTokens();let e=await a.j2.getUserQuota();t("成功获取数据: "+JSON.stringify(e))}catch(s){console.error("测试错误:",s),t("捕获错误: "+s.message+" (code: "+(s.code||"无")+")");let{isAuthError:e,shouldRedirect:r}=(0,c.tu)(s,()=>{u(!0),p({title:"认证失败",description:"会话已过期，正在跳转到登录页面...",variant:"destructive"})});e&&r&&setTimeout(()=>{window.location.href="/login"},2e3)}};return(0,s.jsx)("div",{className:"container mx-auto p-4",children:(0,s.jsxs)(o.Zp,{className:"max-w-2xl mx-auto",children:[(0,s.jsx)(o.aR,{children:(0,s.jsx)(o.ZB,{children:"认证错误处理测试"})}),(0,s.jsxs)(o.Wu,{className:"space-y-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)(n.$,{onClick:h,variant:"outline",children:"测试无效Token"}),(0,s.jsx)(n.$,{onClick:m,variant:"outline",children:"测试无Token"})]}),e&&(0,s.jsxs)("div",{className:"p-4 bg-gray-100 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold mb-2",children:"测试结果:"}),(0,s.jsx)("pre",{className:"text-sm whitespace-pre-wrap",children:e})]}),r&&(0,s.jsxs)("div",{className:"p-4 bg-red-100 border border-red-300 rounded-lg",children:[(0,s.jsx)("h3",{className:"font-semibold text-red-800",children:"认证错误弹窗已触发!"}),(0,s.jsx)("p",{className:"text-red-600",children:"正在准备跳转到登录页面..."})]})]})]})})}},5512:(e,t,r)=>{Promise.resolve().then(r.bind(r,5497))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9255:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\myaitts\\frontend\\app\\test-auth\\page.tsx","default")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[995,625],()=>r(1028));module.exports=s})();