(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{3580:(e,n,t)=>{"use strict";t.d(n,{dj:()=>u});var r=t(2115);let o=0,s=new Map,a=e=>{if(s.has(e))return;let n=setTimeout(()=>{s.delete(e),m({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,n)},i=(e,n)=>{switch(n.type){case"ADD_TOAST":return{...e,toasts:[n.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===n.toast.id?{...e,...n.toast}:e)};case"DISMISS_TOAST":{let{toastId:t}=n;return t?a(t):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===t||void 0===t?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===n.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==n.toastId)}}},c=[],l={toasts:[]};function m(e){l=i(l,e),c.forEach(e=>{e(l)})}function d(e){let{...n}=e,t=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>m({type:"DISMISS_TOAST",toastId:t});return m({type:"ADD_TOAST",toast:{...n,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>m({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function u(){let[e,n]=r.useState(l);return r.useEffect(()=>(c.push(n),()=>{let e=c.indexOf(n);e>-1&&c.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>m({type:"DISMISS_TOAST",toastId:e})}}},7053:(e,n,t)=>{"use strict";t.r(n),t.d(n,{default:()=>e_});var r=t(5155),o=t(9137),s=t.n(o),a=t(2115),i=t(7168),c=t(8482),l=t(5313),m=t(3999);let d=a.forwardRef((e,n)=>{let{className:t,...o}=e;return(0,r.jsxs)(l.bL,{ref:n,className:(0,m.cn)("relative flex w-full touch-none select-none items-center",t),...o,children:[(0,r.jsx)(l.CC,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-secondary",children:(0,r.jsx)(l.Q6,{className:"absolute h-full bg-primary"})}),(0,r.jsx)(l.zi,{className:"block h-5 w-5 rounded-full border-2 border-primary bg-background ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"})]})});d.displayName=l.bL.displayName;let u=e=>({getPosition:(0,a.useCallback)(()=>{let n=e.current;if(!n)return 0;let t=window.getSelection();if(t&&t.rangeCount>0){let e=t.getRangeAt(0),r=e.cloneRange();return r.selectNodeContents(n),r.setEnd(e.endContainer,e.endOffset),r.toString().length}return 0},[e]),setPosition:(0,a.useCallback)(n=>{let t=e.current;if(!t||n<0)return;let r=window.getSelection(),o=document.createRange(),s=0,a=!1;if(0===t.childNodes.length){t.focus();return}!function e(t){if(!a){if(t.nodeType===Node.TEXT_NODE){let e=s+t.length;n<=e?(o.setStart(t,n-s),o.setEnd(t,n-s),a=!0):s=e}else if(t.nodeType===Node.ELEMENT_NODE){for(let n of Array.from(t.childNodes))if(e(n),a)return}}}(t),a||(o.selectNodeContents(t),o.collapse(!1)),r&&(r.removeAllRanges(),r.addRange(o))},[e])}),p=e=>e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\[([a-zA-Z\s]+)\]/g,'<span class="emotion-tag">[$1]</span>'):"",b=e=>{let{value:n,onChange:t,onFocus:o,onBlur:s,placeholder:i,className:c,maxLength:l,onMaxLengthExceeded:m}=e,d=(0,a.useRef)(null),b=(0,a.useRef)(n),{getPosition:g,setPosition:f}=u(d);(0,a.useEffect)(()=>{if(d.current&&n!==b.current){let e=g();d.current.innerHTML=p(n),f(e)}b.current=n},[n,g,f]);let x=(0,a.useCallback)(()=>{if(d.current){let e=d.current.innerText;if(e.length>l){let n=e.substring(0,l),r=g();d.current.innerHTML=p(n),f(Math.min(l,r)),m&&m(e.length,l),t(n);return}t(e)}},[t,l,n,g,f,m]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("style",{children:"\n        .emotion-tag {\n          color: #8B5CF6; /* a nice purple color */\n          font-weight: 600;\n          background-color: rgba(139, 92, 246, 0.05);\n          padding: 2px 1px;\n          border-radius: 4px;\n        }\n      "}),(0,r.jsx)("div",{ref:d,contentEditable:!0,onInput:x,onFocus:o,onBlur:s,className:c,"data-placeholder":i,suppressContentEditableWarning:!0,style:{whiteSpace:"pre-wrap",wordWrap:"break-word"}})]})};var g=t(9852),f=t(6474),x=t(9588),h=t(1007),y=t(9803),w=t(4835),v=t(1539),k=t(7580),j=t(3311),N=t(5670),z=t(5273),A=t(2178),C=t(5690),S=t(1788),T=t(7924),E=t(4416),I=t(1482),L=t(3314),D=t(381),R=t(1243),_=t(4186),P=t(1586),M=t(8749),U=t(2657),O=t(646),F=t(4869),B=t(4357),W=t(9869),K=t(2713),G=t(1284),H=t(9840);async function V(e){try{if(navigator.clipboard&&window.isSecureContext)return await navigator.clipboard.writeText(e),!0;{let n=document.createElement("textarea");n.value=e,n.style.position="fixed",n.style.left="-999999px",n.style.top="-999999px",document.body.appendChild(n),n.focus(),n.select();let t=document.execCommand("copy");return document.body.removeChild(n),t}}catch(e){return console.error("复制失败:",e),!1}}function $(e,n){let t=e.split("\n").map(e=>e.trim()).filter(e=>e.length>0),r=[],o=[],s=[],a=0,i=!1;if(0===t.length)return o.push("导入内容为空"),{validLines:r,errors:o,warnings:s};if(t.forEach((e,t)=>{let c=t+1;if(i){s.push("\uD83D\uDCDD 总字数已达".concat(4e3,"字符上限，第").concat(c,"行及后续内容已被忽略"));return}let l=e.match(/^(.+?)@(.+)$/);if(!l){o.push("第".concat(c,"行格式错误：").concat(e.substring(0,50)).concat(e.length>50?"...":""));return}let[,m,d]=l,u=m.trim(),p=d.trim();if(!u){o.push("第".concat(c,"行声音名称为空"));return}if(!p){o.push("第".concat(c,"行对话内容为空"));return}let b=p;if(p.length>1e3&&(b=p.substring(0,1e3),s.push("✂️ 第".concat(c,"行文本过长(").concat(p.length,"字符)，已自动截断到1000字符"))),a+b.length>4e3){let e=4e3-a;if(e>0)b=b.substring(0,e),s.push("\uD83D\uDCCA 第".concat(c,"行因总字数限制(").concat(4e3,"字符)被截断，保留前").concat(e,"字符")),a=4e3;else{s.push("\uD83D\uDCCA 第".concat(c,"行及后续内容因总字数限制(").concat(4e3,"字符)被忽略")),i=!0;return}i=!0}else a+=b.length;let g=function(e,n){let t=e.toLowerCase().trim();if(!t)return null;let r=n.find(e=>e.name.toLowerCase()===t);if(r||(r=n.find(e=>e.name.toLowerCase().includes(t)||t.includes(e.name.toLowerCase()))))return r;let o=t.replace(/\s+/g,"");return(r=n.find(e=>{let n=e.name.toLowerCase().replace(/\s+/g,"");return n===o||n.includes(o)||o.includes(n)}))||o.length<=3&&(r=n.find(e=>e.name.toLowerCase().split(/\s+/).map(e=>e.charAt(0)).join("")===o))?r:null}(u,n);if(g)r.push({id:Date.now()+Math.random()+t,voice:g.id,text:b});else{var f;s.push("第".concat(c,'行声音"').concat(u,'"不存在，将使用默认声音')),r.push({id:Date.now()+Math.random()+t,voice:(null===(f=n[0])||void 0===f?void 0:f.id)||"",text:b})}}),r.length>0){let e=r.reduce((e,n)=>e+n.text.length,0);e>=4e3?s.push("\uD83D\uDCC8 导入完成：共".concat(r.length,"行对话，总字数").concat(e,"字符（已达上限）")):s.some(e=>e.includes("截断")||e.includes("忽略"))&&s.push("\uD83D\uDCC8 导入完成：共".concat(r.length,"行对话，总字数").concat(e,"字符（含截断处理）"))}return{validLines:r,errors:o,warnings:s}}let q=e=>{let{dialogueLines:n,voices:t,onImport:o,className:s=""}=e,[c,l]=(0,a.useState)({showImportDialog:!1,showStatsDialog:!1,importText:"",importMode:"replace",importPreview:[],importErrors:[],importWarnings:[],isImporting:!1,isCopying:!1,copySuccess:!1,showSample:!1}),m=(0,a.useCallback)(async()=>{if(!c.isCopying){l(e=>({...e,isCopying:!0}));try{let e=function(e,n){let t=e.filter(e=>e.text.trim());if(0===t.length)throw Error("没有可复制的对话内容");return t.map(e=>{let t=n.find(n=>n.id===e.voice),r=(null==t?void 0:t.name)||"未知声音";return"".concat(r,"@").concat(e.text.trim())}).join("\n")}(n,t);if(await V(e))l(e=>({...e,copySuccess:!0})),setTimeout(()=>{l(e=>({...e,copySuccess:!1}))},2e3);else throw Error("复制失败")}catch(e){console.error("复制失败:",e)}finally{l(e=>({...e,isCopying:!1}))}}},[n,t,c.isCopying]),d=(0,a.useCallback)(()=>{let e=function(e){let n=e.slice(0,3),t=["你好！今天过得怎么样？","我今天过得很好，谢谢你的关心！","那真是太好了，有什么特别的事情吗？"];return n.map((e,n)=>"".concat(e.name,"@").concat(t[n]||"这是一个示例对话。")).join("\n")}(t);l(n=>({...n,importText:e,showSample:!0}));let{validLines:n,errors:r,warnings:o}=$(e,t);l(e=>({...e,importPreview:n,importErrors:r,importWarnings:o}))},[t]),u=(0,a.useCallback)(()=>{l(e=>({...e,showStatsDialog:!0}))},[]),p=(0,a.useCallback)(e=>{if(l(n=>({...n,importText:e,showSample:!1})),e.trim()){let{validLines:n,errors:r,warnings:o}=$(e,t);l(e=>({...e,importPreview:n,importErrors:r,importWarnings:o}))}else l(e=>({...e,importPreview:[],importErrors:[],importWarnings:[]}))},[t]),b=(0,a.useCallback)(async()=>{if(0!==c.importPreview.length){l(e=>({...e,isImporting:!0}));try{await o(c.importPreview,c.importMode),l(e=>({...e,showImportDialog:!1,importText:"",importPreview:[],importErrors:[],importWarnings:[],isImporting:!1,showSample:!1}))}catch(e){console.error("导入失败:",e),l(e=>({...e,isImporting:!1}))}}},[c.importPreview,c.importMode,o]),g=n.some(e=>e.text.trim());return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"flex items-center gap-3 ".concat(s),children:[(0,r.jsxs)("button",{onClick:m,disabled:c.isCopying||!g,className:"relative overflow-hidden px-4 py-2 text-xs font-semibold rounded-2xl transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform group ".concat(c.isCopying||!g?"text-gray-400 bg-gray-100 cursor-not-allowed opacity-50":"text-white bg-gradient-to-r from-blue-500 via-blue-600 to-indigo-600 hover:from-blue-600 hover:via-blue-700 hover:to-indigo-700 hover:scale-105 backdrop-blur-sm"),title:g?"复制所有对话到剪贴板":"没有可复制的对话内容",children:[!c.isCopying&&!!g&&(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"}),c.isCopying?(0,r.jsx)("div",{className:"w-3.5 h-3.5 border-2 border-white/30 border-t-white rounded-full animate-spin"}):c.copySuccess?(0,r.jsx)(O.A,{className:"w-3.5 h-3.5 text-green-400"}):(0,r.jsx)(B.A,{className:"w-3.5 h-3.5"}),(0,r.jsx)("span",{className:"relative z-10",children:c.isCopying?"复制中...":c.copySuccess?"已复制":"复制对话"})]}),(0,r.jsxs)("button",{onClick:()=>l(e=>({...e,showImportDialog:!0})),className:"relative overflow-hidden px-4 py-2 text-xs font-semibold text-white bg-gradient-to-r from-emerald-500 via-green-600 to-teal-600 hover:from-emerald-600 hover:via-green-700 hover:to-teal-700 rounded-2xl transition-all duration-300 hover:scale-105 flex items-center gap-2 shadow-lg hover:shadow-xl backdrop-blur-sm group",title:"从剪贴板导入批量对话",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"}),(0,r.jsx)(W.A,{className:"w-3.5 h-3.5 relative z-10"}),(0,r.jsx)("span",{className:"relative z-10",children:"导入对话"})]}),g&&(0,r.jsxs)("button",{onClick:u,className:"relative overflow-hidden px-4 py-2 text-xs font-semibold text-white bg-gradient-to-r from-purple-500 via-violet-600 to-purple-600 hover:from-purple-600 hover:via-violet-700 hover:to-purple-700 rounded-2xl transition-all duration-300 hover:scale-105 flex items-center gap-2 shadow-lg hover:shadow-xl backdrop-blur-sm group",title:"查看对话统计信息",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"}),(0,r.jsx)(K.A,{className:"w-3.5 h-3.5 relative z-10"}),(0,r.jsx)("span",{className:"relative z-10",children:"统计"})]})]}),(0,r.jsx)(H.lG,{open:c.showImportDialog,onOpenChange:e=>l(n=>({...n,showImportDialog:e})),children:(0,r.jsxs)(H.Cf,{className:"max-w-2xl h-[85vh] bg-white/95 backdrop-blur-xl border-0 shadow-2xl flex flex-col overflow-hidden",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-emerald-50/80 via-green-50/60 to-teal-50/80"}),(0,r.jsx)("div",{className:"absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-emerald-200/30 to-green-200/30 rounded-full blur-3xl"}),(0,r.jsx)("div",{className:"absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-teal-200/30 to-emerald-200/30 rounded-full blur-2xl"}),(0,r.jsxs)("div",{className:"relative z-10 flex flex-col h-full min-h-0",children:[(0,r.jsxs)(H.c7,{className:"space-y-4 pb-4 flex-shrink-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-emerald-400 to-green-400 rounded-full blur-lg opacity-50 animate-pulse"}),(0,r.jsx)("div",{className:"relative p-2 bg-gradient-to-r from-emerald-500 to-green-500 rounded-full shadow-lg",children:(0,r.jsx)(W.A,{className:"w-5 h-5 text-white"})})]}),(0,r.jsx)(H.L3,{className:"text-xl font-bold bg-gradient-to-r from-emerald-600 to-green-600 bg-clip-text text-transparent",children:"批量导入对话"})]}),(0,r.jsxs)(H.rr,{className:"text-gray-600 bg-white/60 backdrop-blur-sm rounded-xl p-3 border border-emerald-200/50",children:["每行格式：",(0,r.jsx)("code",{className:"bg-emerald-100 text-emerald-800 px-2 py-1 rounded-lg font-mono text-sm",children:"声音名称@对话内容"}),"。支持的声音名称请参考声音库。"]})]}),(0,r.jsxs)("div",{className:"space-y-6 flex-1 overflow-y-auto min-h-0 pr-2 pb-2",children:[(0,r.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-200/50 shadow-lg",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsx)("label",{className:"text-sm font-semibold text-gray-800",children:"对话内容"}),(0,r.jsxs)("button",{onClick:d,className:"relative overflow-hidden px-3 py-1.5 text-xs font-medium text-white bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 rounded-xl transition-all duration-300 hover:scale-105 flex items-center gap-1.5 shadow-md hover:shadow-lg group",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000"}),(0,r.jsx)(G.A,{className:"w-3 h-3 relative z-10"}),(0,r.jsx)("span",{className:"relative z-10",children:"加载示例"})]})]}),(0,r.jsx)("textarea",{value:c.importText,onChange:e=>p(e.target.value),placeholder:"示例格式：\nAdam@你好！今天过得怎么样？\nJessica@我今天过得很好，谢谢你的关心！\nBrian@那真是太好了，有什么特别的事情吗？",className:"w-full h-32 p-4 border-2 border-emerald-200/60 rounded-xl resize-none focus:ring-4 focus:ring-emerald-500/20 focus:border-emerald-400 transition-all duration-300 text-sm bg-white/80 backdrop-blur-sm placeholder-gray-400"}),c.showSample&&(0,r.jsx)("div",{className:"mt-3 p-3 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200/50",children:(0,r.jsxs)("div",{className:"text-xs text-blue-700 flex items-center gap-2 font-medium",children:[(0,r.jsx)("div",{className:"p-1 bg-blue-500 rounded-full",children:(0,r.jsx)(G.A,{className:"w-3 h-3 text-white"})}),"已加载示例文本，您可以直接导入或修改后导入"]})})]}),(0,r.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-200/50 shadow-lg",children:[(0,r.jsx)("label",{className:"text-sm font-semibold text-gray-800 mb-3 block",children:"导入模式"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3",children:[(0,r.jsxs)("label",{className:"relative cursor-pointer group",children:[(0,r.jsx)("input",{type:"radio",checked:"replace"===c.importMode,onChange:()=>l(e=>({...e,importMode:"replace"})),className:"sr-only"}),(0,r.jsx)("div",{className:"p-3 rounded-xl border-2 transition-all duration-300 ".concat("replace"===c.importMode?"border-orange-400 bg-gradient-to-r from-orange-50 to-red-50 shadow-lg":"border-gray-200 bg-white/80 hover:border-orange-300 hover:bg-orange-50/50"),children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full border-2 flex items-center justify-center ".concat("replace"===c.importMode?"border-orange-500 bg-orange-500":"border-gray-300"),children:"replace"===c.importMode&&(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})}),(0,r.jsx)("span",{className:"text-sm font-medium ".concat("replace"===c.importMode?"text-orange-700":"text-gray-700"),children:"替换现有对话"})]})})]}),(0,r.jsxs)("label",{className:"relative cursor-pointer group",children:[(0,r.jsx)("input",{type:"radio",checked:"append"===c.importMode,onChange:()=>l(e=>({...e,importMode:"append"})),className:"sr-only"}),(0,r.jsx)("div",{className:"p-3 rounded-xl border-2 transition-all duration-300 ".concat("append"===c.importMode?"border-emerald-400 bg-gradient-to-r from-emerald-50 to-green-50 shadow-lg":"border-gray-200 bg-white/80 hover:border-emerald-300 hover:bg-emerald-50/50"),children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 rounded-full border-2 flex items-center justify-center ".concat("append"===c.importMode?"border-emerald-500 bg-emerald-500":"border-gray-300"),children:"append"===c.importMode&&(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})}),(0,r.jsx)("span",{className:"text-sm font-medium ".concat("append"===c.importMode?"text-emerald-700":"text-gray-700"),children:"追加到现有对话"})]})})]})]})]}),(c.importPreview.length>0||c.importErrors.length>0||c.importWarnings.length>0)&&(0,r.jsxs)("div",{className:"bg-white/60 backdrop-blur-sm rounded-2xl p-4 border border-emerald-200/50 shadow-lg",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,r.jsx)("div",{className:"p-1.5 bg-gradient-to-r from-emerald-500 to-green-500 rounded-lg",children:(0,r.jsx)(O.A,{className:"w-4 h-4 text-white"})}),(0,r.jsx)("label",{className:"text-sm font-semibold text-gray-800",children:"预览结果"}),(0,r.jsxs)("div",{className:"ml-auto flex items-center gap-2 text-xs",children:[(0,r.jsxs)("span",{className:"px-2 py-1 bg-green-100 text-green-700 rounded-lg font-medium",children:[c.importPreview.length,"行有效"]}),c.importErrors.length>0&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-red-100 text-red-700 rounded-lg font-medium",children:[c.importErrors.length,"行错误"]}),c.importWarnings.length>0&&(0,r.jsxs)("span",{className:"px-2 py-1 bg-yellow-100 text-yellow-700 rounded-lg font-medium",children:[c.importWarnings.length,"个警告"]})]})]}),(0,r.jsxs)("div",{className:"max-h-32 overflow-y-auto bg-white/80 backdrop-blur-sm rounded-xl p-3 border border-emerald-200/30 space-y-2",children:[c.importPreview.map((e,n)=>{let o=t.find(n=>n.id===e.voice);return(0,r.jsxs)("div",{className:"flex items-start gap-3 p-2 bg-green-50/80 rounded-lg border border-green-200/50",children:[(0,r.jsx)("div",{className:"p-1 bg-green-500 rounded-full",children:(0,r.jsx)(O.A,{className:"w-3 h-3 text-white"})}),(0,r.jsxs)("span",{className:"text-sm flex-1",children:[(0,r.jsx)("span",{className:"font-semibold text-green-700 bg-green-100 px-2 py-0.5 rounded-lg",children:null==o?void 0:o.name}),(0,r.jsx)("span",{className:"mx-2 text-gray-400",children:"→"}),(0,r.jsx)("span",{className:"text-gray-700",children:e.text.length>50?"".concat(e.text.substring(0,50),"..."):e.text})]})]},n)}),c.importWarnings.map((e,n)=>(0,r.jsxs)("div",{className:"flex items-start gap-3 p-2 bg-yellow-50/80 rounded-lg border border-yellow-200/50",children:[(0,r.jsx)("div",{className:"p-1 bg-yellow-500 rounded-full",children:(0,r.jsx)(R.A,{className:"w-3 h-3 text-white"})}),(0,r.jsx)("span",{className:"text-sm text-yellow-700 font-medium",children:e})]},n)),c.importErrors.map((e,n)=>(0,r.jsxs)("div",{className:"flex items-start gap-3 p-2 bg-red-50/80 rounded-lg border border-red-200/50",children:[(0,r.jsx)("div",{className:"p-1 bg-red-500 rounded-full",children:(0,r.jsx)(R.A,{className:"w-3 h-3 text-white"})}),(0,r.jsx)("span",{className:"text-sm text-red-700 font-medium",children:e})]},n))]})]})]}),(0,r.jsxs)(H.Es,{className:"flex gap-3 pt-4 pb-2 px-2 flex-shrink-0 border-t border-emerald-200/50 mt-4 bg-white/80 backdrop-blur-sm",children:[(0,r.jsx)(i.$,{variant:"outline",onClick:()=>l(e=>({...e,showImportDialog:!1,importText:"",importPreview:[],importErrors:[],importWarnings:[],showSample:!1})),className:"flex-1 border-2 border-gray-300 hover:border-gray-400 hover:bg-gray-50 transition-all duration-300 rounded-xl py-2.5 font-medium",children:"取消"}),(0,r.jsx)(i.$,{onClick:b,disabled:0===c.importPreview.length||c.isImporting,className:"flex-1 min-w-[140px] bg-gradient-to-r from-emerald-500 via-green-600 to-teal-600 hover:from-emerald-600 hover:via-green-700 hover:to-teal-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 rounded-xl py-2.5 font-semibold disabled:opacity-50 disabled:cursor-not-allowed",children:c.isImporting?(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)("div",{className:"w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),(0,r.jsx)("span",{children:"导入中..."})]}):(0,r.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,r.jsx)(W.A,{className:"w-4 h-4"}),(0,r.jsxs)("span",{children:["确认导入(",c.importPreview.length,"行)"]})]})})]})]})]})}),(0,r.jsx)(H.lG,{open:c.showStatsDialog,onOpenChange:e=>l(n=>({...n,showStatsDialog:e})),children:(0,r.jsxs)(H.Cf,{className:"max-w-lg",children:[(0,r.jsx)(H.c7,{children:(0,r.jsxs)(H.L3,{className:"flex items-center gap-2",children:[(0,r.jsx)(K.A,{className:"w-5 h-5 text-purple-600"}),"对话统计信息"]})}),(()=>{let e=function(e,n){let t=e.length,r=e.reduce((e,n)=>e+n.text.length,0),o=t>0?Math.round(r/t):0,s=new Map;e.forEach(e=>{let n=s.get(e.voice)||0;s.set(e.voice,n+1)});let a=Array.from(s.entries()).map(e=>{let[r,o]=e;return{voice:n.find(e=>e.id===r)||{id:r,name:"未知声音",gender:"neutral",description:""},count:o,percentage:Math.round(o/t*100)}}).sort((e,n)=>n.count-e.count);return{totalLines:t,totalCharacters:r,voiceDistribution:a,averageLength:o}}(n,t);return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{className:"bg-blue-50 p-3 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:e.totalLines}),(0,r.jsx)("div",{className:"text-sm text-blue-700",children:"对话行数"})]}),(0,r.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-green-600",children:e.totalCharacters}),(0,r.jsx)("div",{className:"text-sm text-green-700",children:"总字符数"})]})]}),(0,r.jsxs)("div",{className:"bg-purple-50 p-3 rounded-lg",children:[(0,r.jsx)("div",{className:"text-2xl font-bold text-purple-600",children:e.averageLength}),(0,r.jsx)("div",{className:"text-sm text-purple-700",children:"平均每行字符数"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"text-sm font-medium text-gray-700 mb-2",children:"声音使用分布"}),(0,r.jsx)("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:e.voiceDistribution.map((e,n)=>(0,r.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("male"===e.voice.gender?"bg-blue-500":"female"===e.voice.gender?"bg-pink-500":"bg-gray-500")}),(0,r.jsx)("span",{className:"text-sm font-medium",children:e.voice.name})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("span",{className:"text-sm text-gray-600",children:[e.count,"次"]}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.percentage,"%)"]})]})]},n))})]})]})})(),(0,r.jsx)(H.Es,{children:(0,r.jsx)(i.$,{variant:"outline",onClick:()=>l(e=>({...e,showStatsDialog:!1})),children:"关闭"})})]})})]})},Y=e=>({getPosition:(0,a.useCallback)(()=>{let n=e.current;if(!n)return 0;let t=window.getSelection();if(t&&t.rangeCount>0){let e=t.getRangeAt(0),r=e.cloneRange();return r.selectNodeContents(n),r.setEnd(e.endContainer,e.endOffset),r.toString().length}return 0},[e]),setPosition:(0,a.useCallback)(n=>{let t=e.current;if(!t||n<0)return;let r=window.getSelection(),o=document.createRange(),s=0,a=!1;if(0===t.childNodes.length){t.focus();return}!function e(t){if(!a){if(t.nodeType===Node.TEXT_NODE){let e=s+t.length;n<=e?(o.setStart(t,n-s),o.setEnd(t,n-s),a=!0):s=e}else if(t.nodeType===Node.ELEMENT_NODE){for(let n of Array.from(t.childNodes))if(e(n),a)return}}}(t),a||(o.selectNodeContents(t),o.collapse(!1)),r&&(r.removeAllRanges(),r.addRange(o))},[e])}),J=e=>e?e.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/\[([a-zA-Z\s]+)\]/g,'<span class="emotion-tag">[$1]</span>'):"",Z=e=>{let{value:n,onChange:t,onFocus:o,onBlur:s,placeholder:i,className:c,maxLength:l=1e3}=e,m=(0,a.useRef)(null),d=(0,a.useRef)(""),{getPosition:u,setPosition:p}=Y(m);(0,a.useEffect)(()=>{if(m.current){let e=J(n);if(n!==d.current||m.current.innerHTML!==e){let n=u();m.current.innerHTML=e,p(n)}}d.current=n},[n,u,p]);let b=(0,a.useCallback)(()=>{if(m.current){let e=m.current.innerText;if(e.length>l){let e=u();m.current.innerHTML=J(n),p(Math.min(n.length,e));return}t(e)}},[t,l,n,u,p]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("style",{children:"\n        .emotion-tag {\n          color: #8B5CF6; /* a nice purple color */\n          font-weight: 600;\n          background-color: rgba(139, 92, 246, 0.05);\n          padding: 2px 1px;\n          border-radius: 4px;\n        }\n      "}),(0,r.jsx)("div",{ref:m,contentEditable:!0,onInput:b,onFocus:o,onBlur:s,className:c,"data-placeholder":i,suppressContentEditableWarning:!0,style:{whiteSpace:"pre-wrap",wordWrap:"break-word"}})]})},X=e=>{let{line:n,index:t,voices:o,voiceIconMapping:s,voiceIcons:a,isActive:c,onSelectLine:l,onUpdateText:m,onRemoveLine:d,canRemove:u,onTextInputFocus:p,onEditVoice:b}=e,g=o.find(e=>e.id===n.voice);return(0,r.jsxs)("div",{"data-dialogue-line-id":n.id,className:"group relative flex items-center gap-3 p-3 pl-8 rounded-xl border transition-all duration-300 ".concat(c?"border-purple-400 bg-gradient-to-r from-purple-50/50 to-blue-50/50 shadow-md":"border-gray-200/80 hover:border-gray-300 hover:bg-gray-50/30"),children:[(0,r.jsxs)("button",{onClick:()=>null==b?void 0:b(n.id),className:"relative w-7 h-7 rounded-full overflow-hidden shadow-md transition-all duration-300 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-purple-400 flex-shrink-0 ".concat(c?"ring-2 ring-purple-300 scale-105":"hover:ring-2 hover:ring-purple-200"),title:"点击为 ".concat((null==g?void 0:g.name)||"说话者 ".concat(t+1)," 选择声音"),children:[(0,r.jsx)("img",{src:s[n.voice]||a[0],alt:null==g?void 0:g.name,className:"w-full h-full object-cover",onError:e=>{let n=e.target;n.style.display="none";let t=n.parentElement;t&&g&&(t.innerHTML='\n                <div class="w-full h-full rounded-full flex items-center justify-center text-white font-bold text-sm '.concat("male"===g.gender?"bg-gradient-to-br from-blue-500 to-blue-700":"female"===g.gender?"bg-gradient-to-br from-pink-500 to-pink-700":"bg-gradient-to-br from-gray-500 to-gray-700",'">\n                  ').concat(g.name.charAt(0).toUpperCase(),"\n                </div>\n              "))}}),c&&(0,r.jsx)("div",{className:"absolute -top-1 -right-1 w-3 h-3 bg-purple-500 rounded-full border border-white",children:(0,r.jsx)("div",{className:"absolute inset-0 bg-purple-400 rounded-full animate-ping opacity-75"})})]}),(0,r.jsxs)("div",{className:"flex-shrink-0 min-w-0 w-20",children:[(0,r.jsx)("div",{className:"font-medium text-sm truncate transition-colors duration-300 ".concat(c?"text-purple-700":"text-gray-700"),children:(null==g?void 0:g.name)||"未选择"}),g&&(0,r.jsx)("div",{className:"text-xs text-gray-500",children:"male"===g.gender?"♂ 男声":"female"===g.gender?"♀ 女声":"⚲ 中性"})]}),(0,r.jsx)("div",{className:"flex-1 min-w-0",children:(0,r.jsx)(Z,{value:n.text,onChange:e=>m(n.id,e),onFocus:()=>null==p?void 0:p(n.id),placeholder:"".concat((null==g?void 0:g.name)||"说话者 ".concat(t+1)," 说..."),className:"w-full bg-white/80 text-base py-2.5 px-3 border rounded-md transition-all duration-300 min-h-[42px] outline-none ".concat(c?"border-purple-300 bg-white focus:border-purple-400":"border-gray-200 focus:border-blue-400 focus:bg-white"),maxLength:1e3})}),(0,r.jsx)("div",{className:"flex-shrink-0 text-xs text-gray-400 w-12 text-right",children:n.text.length}),u&&(0,r.jsx)(i.$,{variant:"ghost",size:"icon",onClick:()=>d(n.id),className:"flex-shrink-0 w-8 h-8 text-gray-400 hover:bg-red-100 hover:text-red-600 opacity-0 group-hover:opacity-100 transition-all duration-300",title:"删除此对话",children:(0,r.jsx)(E.A,{className:"w-4 h-4"})}),(0,r.jsx)("div",{className:"absolute -top--1 -left-0 w-6 h-6 text-white text-xs font-bold rounded-full flex items-center justify-center shadow-lg border-2 border-white transition-all duration-300 ".concat(c?"bg-gradient-to-r from-purple-500 to-pink-500 scale-110 shadow-xl":"bg-gradient-to-r from-blue-500 to-purple-500 group-hover:scale-105"),children:t+1})]})},Q=e=>{let{dialogueLines:n,voices:t,voiceIconMapping:o,voiceIcons:s,activeDialogueLineId:i,onSelectLine:c,onUpdateText:l,onRemoveLine:m,onTextInputFocus:d,onEditVoice:u,itemHeight:p=80,containerHeight:b=350,overscan:g=2,className:f=""}=e,[x,h]=(0,a.useState)(0),y=(0,a.useRef)(null),w=(0,a.useRef)(!1),v=(0,a.useRef)(),k=Math.ceil(b/p),j=Math.max(0,Math.floor(x/p)-g),N=Math.min(n.length,j+k+2*g),z=n.slice(j,N),A=n.length*p,C=(0,a.useCallback)(e=>{h(e.currentTarget.scrollTop),w.current=!0,v.current&&clearTimeout(v.current),v.current=setTimeout(()=>{w.current=!1},150)},[]),S=(0,a.useCallback)(e=>{let t=n.findIndex(n=>n.id===e);if(-1!==t&&y.current){let e=t*p;y.current.scrollTo({top:e,behavior:"smooth"})}},[n,p]);return(0,a.useEffect)(()=>{if(null!==i&&!w.current){let e=n.findIndex(e=>e.id===i);if(-1!==e){let n=e*p,t=n+p,r=x+b;(n<x||t>r)&&S(i)}}},[i,n,p,b,x,S]),(0,a.useEffect)(()=>()=>{v.current&&clearTimeout(v.current)},[]),(0,r.jsx)("div",{ref:y,className:"overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 ".concat(f),style:{height:b},onScroll:C,children:(0,r.jsx)("div",{style:{height:A,position:"relative"},children:(0,r.jsx)("div",{style:{transform:"translateY(".concat(j*p,"px)"),position:"absolute",top:0,left:0,right:0},children:z.map((e,a)=>(0,r.jsx)("div",{style:{height:p,paddingBottom:"12px"},children:(0,r.jsx)(X,{line:e,index:j+a,voices:t,voiceIconMapping:o,voiceIcons:s,isActive:i===e.id,onSelectLine:c,onUpdateText:l,onRemoveLine:m,canRemove:n.length>1,onTextInputFocus:d,onEditVoice:u})},e.id))})})})},ee=e=>{let{dialogueLines:n,voices:t,voiceIconMapping:o,voiceIcons:s,activeDialogueLineId:a,onSelectLine:i,onUpdateText:c,onRemoveLine:l,onTextInputFocus:m,onEditVoice:d,containerHeight:u=350,className:p=""}=e;return(0,r.jsx)("div",{className:"space-y-3 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 pr-2 ".concat(p),style:{maxHeight:u},children:n.map((e,u)=>(0,r.jsx)(X,{line:e,index:u,voices:t,voiceIconMapping:o,voiceIcons:s,isActive:a===e.id,onSelectLine:i,onUpdateText:c,onRemoveLine:l,canRemove:n.length>1,onTextInputFocus:m,onEditVoice:d},e.id))})},en=e=>{let{dialogueLines:n,virtualThreshold:t=20,...o}=e;return n.length>t?(0,r.jsx)(Q,{dialogueLines:n,...o}):(0,r.jsx)(ee,{dialogueLines:n,...o})};var et=t(6194),er=t(1886),eo=t(3580),es=t(7492);function ea(e){let{className:n=""}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"jsx-e0e68512b4dc21ba "+"absolute inset-0 aurora-layer-1 ".concat(n)}),(0,r.jsx)("div",{className:"jsx-e0e68512b4dc21ba "+"absolute inset-0 aurora-layer-2 ".concat(n)}),(0,r.jsx)("div",{className:"jsx-e0e68512b4dc21ba "+"absolute inset-0 aurora-layer-3 ".concat(n)}),(0,r.jsx)("div",{className:"jsx-e0e68512b4dc21ba "+"absolute inset-0 aurora-layer-4 ".concat(n)}),(0,r.jsx)(s(),{id:"e0e68512b4dc21ba",children:".aurora-layer-1.jsx-e0e68512b4dc21ba{background:-webkit-linear-gradient(45deg,rgba(75,112,233,.8),rgba(59,130,246,.6),rgba(139,92,246,.7),rgba(201,100,57,.5));background:-moz-linear-gradient(45deg,rgba(75,112,233,.8),rgba(59,130,246,.6),rgba(139,92,246,.7),rgba(201,100,57,.5));background:-o-linear-gradient(45deg,rgba(75,112,233,.8),rgba(59,130,246,.6),rgba(139,92,246,.7),rgba(201,100,57,.5));background:linear-gradient(45deg,rgba(75,112,233,.8),rgba(59,130,246,.6),rgba(139,92,246,.7),rgba(201,100,57,.5));-webkit-background-size:400%400%;-moz-background-size:400%400%;-o-background-size:400%400%;background-size:400%400%;-webkit-animation:aurora-flow-1 8s ease-in-out infinite;-moz-animation:aurora-flow-1 8s ease-in-out infinite;-o-animation:aurora-flow-1 8s ease-in-out infinite;animation:aurora-flow-1 8s ease-in-out infinite;-webkit-border-radius:inherit;-moz-border-radius:inherit;border-radius:inherit}.aurora-layer-2.jsx-e0e68512b4dc21ba{background:-webkit-linear-gradient(135deg,rgba(59,130,246,.6),rgba(139,92,246,.4),rgba(75,112,233,.7),rgba(201,100,57,.5));background:-moz-linear-gradient(135deg,rgba(59,130,246,.6),rgba(139,92,246,.4),rgba(75,112,233,.7),rgba(201,100,57,.5));background:-o-linear-gradient(135deg,rgba(59,130,246,.6),rgba(139,92,246,.4),rgba(75,112,233,.7),rgba(201,100,57,.5));background:linear-gradient(-45deg,rgba(59,130,246,.6),rgba(139,92,246,.4),rgba(75,112,233,.7),rgba(201,100,57,.5));-webkit-background-size:350%350%;-moz-background-size:350%350%;-o-background-size:350%350%;background-size:350%350%;animation:aurora-flow-2 12s ease-in-out infinite reverse;-webkit-border-radius:inherit;-moz-border-radius:inherit;border-radius:inherit}.aurora-layer-3.jsx-e0e68512b4dc21ba{background:-webkit-linear-gradient(left,rgba(139,92,246,.5),rgba(75,112,233,.6),rgba(59,130,246,.4),rgba(201,100,57,.7));background:-moz-linear-gradient(left,rgba(139,92,246,.5),rgba(75,112,233,.6),rgba(59,130,246,.4),rgba(201,100,57,.7));background:-o-linear-gradient(left,rgba(139,92,246,.5),rgba(75,112,233,.6),rgba(59,130,246,.4),rgba(201,100,57,.7));background:linear-gradient(90deg,rgba(139,92,246,.5),rgba(75,112,233,.6),rgba(59,130,246,.4),rgba(201,100,57,.7));-webkit-background-size:300%300%;-moz-background-size:300%300%;-o-background-size:300%300%;background-size:300%300%;-webkit-animation:aurora-flow-3 10s ease-in-out infinite;-moz-animation:aurora-flow-3 10s ease-in-out infinite;-o-animation:aurora-flow-3 10s ease-in-out infinite;animation:aurora-flow-3 10s ease-in-out infinite;-webkit-border-radius:inherit;-moz-border-radius:inherit;border-radius:inherit}.aurora-layer-4.jsx-e0e68512b4dc21ba{background:-webkit-radial-gradient(center,ellipse,rgba(139,92,246,.3)0%,rgba(75,112,233,.4)25%,rgba(59,130,246,.3)50%,rgba(201,100,57,.2)75%,transparent 100%);background:-moz-radial-gradient(center,ellipse,rgba(139,92,246,.3)0%,rgba(75,112,233,.4)25%,rgba(59,130,246,.3)50%,rgba(201,100,57,.2)75%,transparent 100%);background:-o-radial-gradient(center,ellipse,rgba(139,92,246,.3)0%,rgba(75,112,233,.4)25%,rgba(59,130,246,.3)50%,rgba(201,100,57,.2)75%,transparent 100%);background:radial-gradient(ellipse at center,rgba(139,92,246,.3)0%,rgba(75,112,233,.4)25%,rgba(59,130,246,.3)50%,rgba(201,100,57,.2)75%,transparent 100%);-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:aurora-pulse 6s ease-in-out infinite alternate;-moz-animation:aurora-pulse 6s ease-in-out infinite alternate;-o-animation:aurora-pulse 6s ease-in-out infinite alternate;animation:aurora-pulse 6s ease-in-out infinite alternate;-webkit-border-radius:inherit;-moz-border-radius:inherit;border-radius:inherit}@-webkit-keyframes aurora-flow-1{0%,100%{background-position:0%50%;-webkit-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}25%{background-position:100%0%;-webkit-transform:translatex(2px)scale(1.02);transform:translatex(2px)scale(1.02)}50%{background-position:100%100%;-webkit-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}75%{background-position:0%100%;-webkit-transform:translatex(-2px)scale(.98);transform:translatex(-2px)scale(.98)}}@-moz-keyframes aurora-flow-1{0%,100%{background-position:0%50%;-moz-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}25%{background-position:100%0%;-moz-transform:translatex(2px)scale(1.02);transform:translatex(2px)scale(1.02)}50%{background-position:100%100%;-moz-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}75%{background-position:0%100%;-moz-transform:translatex(-2px)scale(.98);transform:translatex(-2px)scale(.98)}}@-o-keyframes aurora-flow-1{0%,100%{background-position:0%50%;-o-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}25%{background-position:100%0%;-o-transform:translatex(2px)scale(1.02);transform:translatex(2px)scale(1.02)}50%{background-position:100%100%;-o-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}75%{background-position:0%100%;-o-transform:translatex(-2px)scale(.98);transform:translatex(-2px)scale(.98)}}@keyframes aurora-flow-1{0%,100%{background-position:0%50%;-webkit-transform:translatex(0)scale(1);-moz-transform:translatex(0)scale(1);-o-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}25%{background-position:100%0%;-webkit-transform:translatex(2px)scale(1.02);-moz-transform:translatex(2px)scale(1.02);-o-transform:translatex(2px)scale(1.02);transform:translatex(2px)scale(1.02)}50%{background-position:100%100%;-webkit-transform:translatex(0)scale(1);-moz-transform:translatex(0)scale(1);-o-transform:translatex(0)scale(1);transform:translatex(0)scale(1)}75%{background-position:0%100%;-webkit-transform:translatex(-2px)scale(.98);-moz-transform:translatex(-2px)scale(.98);-o-transform:translatex(-2px)scale(.98);transform:translatex(-2px)scale(.98)}}@-webkit-keyframes aurora-flow-2{0%,100%{background-position:100%0%;opacity:.8}33%{background-position:0%100%;opacity:.6}66%{background-position:100%100%;opacity:.9}}@-moz-keyframes aurora-flow-2{0%,100%{background-position:100%0%;opacity:.8}33%{background-position:0%100%;opacity:.6}66%{background-position:100%100%;opacity:.9}}@-o-keyframes aurora-flow-2{0%,100%{background-position:100%0%;opacity:.8}33%{background-position:0%100%;opacity:.6}66%{background-position:100%100%;opacity:.9}}@keyframes aurora-flow-2{0%,100%{background-position:100%0%;opacity:.8}33%{background-position:0%100%;opacity:.6}66%{background-position:100%100%;opacity:.9}}@-webkit-keyframes aurora-flow-3{0%,100%{background-position:0%0%;-webkit-transform:rotate(0deg);transform:rotate(0deg)}50%{background-position:100%100%;-webkit-transform:rotate(1deg);transform:rotate(1deg)}}@-moz-keyframes aurora-flow-3{0%,100%{background-position:0%0%;-moz-transform:rotate(0deg);transform:rotate(0deg)}50%{background-position:100%100%;-moz-transform:rotate(1deg);transform:rotate(1deg)}}@-o-keyframes aurora-flow-3{0%,100%{background-position:0%0%;-o-transform:rotate(0deg);transform:rotate(0deg)}50%{background-position:100%100%;-o-transform:rotate(1deg);transform:rotate(1deg)}}@keyframes aurora-flow-3{0%,100%{background-position:0%0%;-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}50%{background-position:100%100%;-webkit-transform:rotate(1deg);-moz-transform:rotate(1deg);-o-transform:rotate(1deg);transform:rotate(1deg)}}@-webkit-keyframes aurora-pulse{0%{background-position:0%0%;opacity:.4;-webkit-transform:scale(1);transform:scale(1)}50%{background-position:100%100%;opacity:.8;-webkit-transform:scale(1.05);transform:scale(1.05)}100%{background-position:0%0%;opacity:.4;-webkit-transform:scale(1);transform:scale(1)}}@-moz-keyframes aurora-pulse{0%{background-position:0%0%;opacity:.4;-moz-transform:scale(1);transform:scale(1)}50%{background-position:100%100%;opacity:.8;-moz-transform:scale(1.05);transform:scale(1.05)}100%{background-position:0%0%;opacity:.4;-moz-transform:scale(1);transform:scale(1)}}@-o-keyframes aurora-pulse{0%{background-position:0%0%;opacity:.4;-o-transform:scale(1);transform:scale(1)}50%{background-position:100%100%;opacity:.8;-o-transform:scale(1.05);transform:scale(1.05)}100%{background-position:0%0%;opacity:.4;-o-transform:scale(1);transform:scale(1)}}@keyframes aurora-pulse{0%{background-position:0%0%;opacity:.4;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{background-position:100%100%;opacity:.8;-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05)}100%{background-position:0%0%;opacity:.4;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}}"})]})}let ei=e=>{let{filteredVoices:n,onSelectVoice:t,currentVoiceId:o,previewingVoice:s,handleVoicePreview:a,voiceIconMapping:i,voiceIcons:c,listHeightClass:l="max-h-80",showSelectionIndicator:m=!0}=e;return(0,r.jsx)("div",{className:"".concat(l," overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"),children:n.length>0?n.map(e=>(0,r.jsxs)("div",{"data-voice-id":e.id,className:"relative p-2.5 border-b border-gray-100 last:border-b-0 cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-blue-50/50 group/item ".concat(o===e.id?"bg-gradient-to-r from-blue-50 to-purple-50 border-blue-100":""),onClick:()=>t(e.id),children:[(0,r.jsxs)("div",{className:"flex items-center gap-2.5",children:[(0,r.jsx)("div",{className:"relative w-10 h-10 rounded-full overflow-hidden shadow-lg transition-all duration-300 group-hover/item:scale-110 group-hover/item:rotate-12 ".concat(o===e.id?"ring-3 ring-blue-300 scale-110":""),children:i[e.id]?(0,r.jsx)("img",{src:i[e.id],alt:e.name,className:"w-full h-full object-cover",onError:n=>{let t=n.target;t.style.display="none";let r=t.parentElement;r&&(r.innerHTML='\n                          <div class="w-full h-full rounded-full flex items-center justify-center text-white font-bold '.concat("male"===e.gender?"bg-gradient-to-br from-blue-500 to-blue-700":"female"===e.gender?"bg-gradient-to-br from-pink-500 to-pink-700":"bg-gradient-to-br from-gray-500 to-gray-700",'">\n                            ').concat(e.name.charAt(0).toUpperCase(),"\n                          </div>\n                        "))}}):(0,r.jsx)("div",{className:"w-full h-full rounded-full flex items-center justify-center text-white font-bold animate-pulse ".concat("male"===e.gender?"bg-gradient-to-br from-blue-400 to-blue-600":"female"===e.gender?"bg-gradient-to-br from-pink-400 to-pink-600":"bg-gradient-to-br from-gray-400 to-gray-600"),children:e.name.charAt(0).toUpperCase()})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"font-semibold text-gray-900 text-base truncate group-hover/item:text-blue-700 transition-colors duration-300",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center gap-1 px-2 py-0.5 text-xs font-medium rounded-full flex-shrink-0 ".concat("male"===e.gender?"bg-blue-100 text-blue-700 border border-blue-200":"female"===e.gender?"bg-pink-100 text-pink-700 border border-pink-200":"bg-gray-100 text-gray-700 border border-gray-200"),children:[(0,r.jsx)("div",{className:"w-2 h-2 rounded-full ".concat("male"===e.gender?"bg-blue-500":"female"===e.gender?"bg-pink-500":"bg-gray-500")}),(0,r.jsx)("span",{children:"male"===e.gender?"男生":"female"===e.gender?"女生":"中性"})]}),o===e.id&&(0,r.jsx)("div",{className:"w-3 h-3 bg-blue-500 rounded-full animate-ping flex-shrink-0 ml-10"})]}),(0,r.jsx)("div",{className:"text-sm leading-relaxed truncate transition-colors duration-300 ".concat("male"===e.gender?"text-blue-600 group-hover/item:text-blue-700":"female"===e.gender?"text-pink-600 group-hover/item:text-pink-700":"text-gray-600 group-hover/item:text-gray-700"),children:e.description})]}),(0,r.jsx)("button",{onClick:n=>{n.stopPropagation(),a(e.preview||null,e.id)},disabled:!e.preview,className:"button-hover-optimized w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 hover:scale-125 hover:rotate-12 group/play shadow-lg disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 ".concat(s===e.id?"bg-gradient-to-r from-green-100 to-green-200 hover:from-green-200 hover:to-green-300":"bg-gradient-to-r from-gray-100 to-gray-200 hover:from-blue-100 hover:to-blue-200"),children:s===e.id?(0,r.jsx)("div",{className:"flex items-center justify-center",children:(0,r.jsx)("div",{className:"w-1.5 h-1.5 bg-green-500 rounded-full animate-pulse"})}):(0,r.jsx)(C.A,{className:"w-4 h-4 text-gray-600 group-hover/play:text-blue-600 ml-0.5 transition-colors duration-300"})})]}),m&&o===e.id&&(0,r.jsx)("div",{className:"absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-b from-blue-400 via-purple-500 to-pink-400 rounded-r animate-pulse"})]},e.id)):(0,r.jsx)("div",{className:"p-8 text-center text-gray-500",children:(0,r.jsx)("p",{children:"没有找到匹配的声音。"})})})},ec=["https://eleven-public-cdn.elevenlabs.io/payloadcms/ox7fne3bkeo-brian.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/twhwqss70ic-alice.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/4vvqikmli2m-bill.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/gwb2kbm395-callum.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/ixo4og3542i-charlie.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/zw9ec3ktkch-charlotte.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/xu3c1krvtn-chris.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/toavylo6g7-daniel.jpg"];function el(e,n){let t=!(arguments.length>2)||void 0===arguments[2]||arguments[2];return{type:e,message:n,retryable:t}}var em=t(6517),ed=t(9323),eu=t(4788),ep=t(3904);let eb=e=>{let{error:n,onRetry:t,className:o=""}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center min-h-screen ".concat(o),children:(0,r.jsx)(c.Zp,{className:"w-full max-w-md mx-4",children:(0,r.jsxs)(c.Wu,{className:"flex flex-col items-center justify-center p-8",children:[(0,r.jsx)("div",{className:"mb-6",children:(()=>{switch(n.type){case"network":return(0,r.jsx)(em.A,{className:"w-12 h-12 text-red-500"});case"parse":return(0,r.jsx)(ed.A,{className:"w-12 h-12 text-orange-500"});case"empty":return(0,r.jsx)(R.A,{className:"w-12 h-12 text-yellow-500"});default:return(0,r.jsx)(eu.A,{className:"w-12 h-12 text-gray-500"})}})()}),(0,r.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2 text-center",children:(()=>{switch(n.type){case"network":return"网络连接失败";case"parse":return"数据格式错误";case"empty":return"暂无声音数据";default:return"加载失败"}})()}),(0,r.jsx)("p",{className:"text-sm text-gray-600 text-center mb-6",children:(()=>{switch(n.type){case"network":return"无法连接到服务器，请检查网络连接后重试";case"parse":return"服务器返回的数据格式不正确，请联系技术支持";case"empty":return"当前没有可用的声音选项，请稍后再试或联系管理员";default:return"发生了未知错误，请尝试刷新页面或联系技术支持"}})()}),!1,(0,r.jsxs)("div",{className:"flex flex-col gap-3 w-full",children:[n.retryable&&(0,r.jsxs)(i.$,{onClick:t,className:"w-full flex items-center gap-2",variant:"default",children:[(0,r.jsx)(ep.A,{className:"w-4 h-4"}),"重试加载"]}),(0,r.jsx)(i.$,{onClick:()=>window.location.reload(),variant:"outline",className:"w-full",children:"刷新页面"})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 text-center mt-4",children:"如果问题持续存在，请联系技术支持"})]})})})};var eg=t(5968),ef=t(5196);let ex="tts_task_center_tasks",eh=(0,a.forwardRef)((e,n)=>{let{className:t="",getActualTaskIds:o}=e,[s,c]=(0,a.useState)(!1),[l,m]=(0,a.useState)([]),[d,u]=(0,a.useState)(""),[p,b]=(0,a.useState)([]),[f,x]=(0,a.useState)(null),h=(0,a.useCallback)(e=>{try{localStorage.setItem(ex,JSON.stringify(e))}catch(e){console.error("Failed to save tasks to localStorage:",e)}},[]);(0,a.useEffect)(()=>{try{let e=localStorage.getItem(ex);if(e){let n=JSON.parse(e);m(n)}}catch(e){console.error("Failed to load tasks from localStorage:",e)}},[]),(0,a.useEffect)(()=>{if(d.trim()){let e=d.toLowerCase().trim();b(l.filter(n=>n.taskId.toLowerCase().includes(e)))}else b(l)},[l,d]);let y=(0,a.useCallback)(e=>{let n={taskId:e,createdAt:Date.now(),status:"processing"};m(e=>{let t=[n,...e];return h(t),t})},[h]),w=(0,a.useCallback)((e,n,t)=>{m(r=>{let o=r.map(r=>r.taskId===e?{...r,status:n,downloadUrl:t,isRefreshing:!1}:r);return h(o),o})},[h]);(0,a.useImperativeHandle)(n,()=>({addTask:y,updateTaskStatus:w}),[y,w]);let v=(0,a.useCallback)(async e=>{try{m(n=>n.map(n=>n.taskId===e?{...n,isRefreshing:!0}:n));let n=er.tC.getAccessToken();if(!n)throw Error("未找到访问令牌，请重新登录");let t=o?o(e):[e];console.log("[TASK-CENTER] Refresh request:",{displayTaskId:e,actualTaskIds:t});let r=null,s=null;for(let e of t)try{console.log("[TASK-CENTER] Trying to fetch status for physical task: ".concat(e));let t=await fetch("".concat("https://my.aispeak.top","/api/tts/status/").concat(e),{method:"GET",headers:{Authorization:"Bearer ".concat(n),"Content-Type":"application/json"}});if(!t.ok){let n=await t.json().catch(()=>({}));r=Error(n.error||"HTTP ".concat(t.status,": ").concat(t.statusText)),console.log("[TASK-CENTER] Physical task ".concat(e," failed:"),r.message);continue}let o=await t.json();if("complete"===o.status||"completed"===o.status){s=o,console.log("[TASK-CENTER] Found successful task: ".concat(e),o);break}if("processing"===o.status){s=o,console.log("[TASK-CENTER] Found processing task: ".concat(e),o);break}r=Error("Task ".concat(e," status: ").concat(o.status)),console.log("[TASK-CENTER] Physical task ".concat(e," not successful:"),o.status);continue}catch(n){r=n,console.log("[TASK-CENTER] Error fetching physical task ".concat(e,":"),n.message);continue}if(!s)throw r||Error("所有物理任务ID查询都失败了");let a=s;if("complete"===a.status||"completed"===a.status){let n=a.audioUrl||a.downloadUrl||"https://r2-assets.aispeak.top/audios/".concat(e,".mp3");w(e,"complete",n),console.log("[TASK-CENTER] Updated display task ".concat(e," to complete"))}else"processing"===a.status||"pending"===a.status||"running"===a.status?(w(e,"processing"),console.log("[TASK-CENTER] Updated display task ".concat(e," to processing"))):"failed"===a.status||"error"===a.status?(w(e,"failed"),console.log("[TASK-CENTER] Updated display task ".concat(e," to failed"))):(w(e,"failed"),console.log("[TASK-CENTER] Updated display task ".concat(e," to failed (unknown status: ").concat(a.status,")")))}catch(n){console.error("[TASK-CENTER] Failed to fetch task status:",n),m(n=>n.map(n=>n.taskId===e?{...n,isRefreshing:!1}:n))}},[w,o]),k=(0,a.useCallback)(async e=>{try{await navigator.clipboard.writeText(e),x(e),setTimeout(()=>x(null),2e3)}catch(e){console.error("Failed to copy task ID:",e)}},[]),j=(0,a.useCallback)(async(e,n)=>{try{console.log("[TASK-CENTER] Initiating secure download:",{downloadUrl:e,taskId:n});let t=new Date,r=t.getFullYear(),o=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0"),a=String(t.getHours()).padStart(2,"0"),i=String(t.getMinutes()).padStart(2,"0"),c=String(t.getSeconds()).padStart(2,"0"),l="tts_".concat(r).concat(o).concat(s,"_").concat(a).concat(i).concat(c,".mp3");if(e){let n=er.tC.getAccessToken();if(!n)throw console.error("[TASK-CENTER] No token available for download"),Error("认证失败，请重新登录");let t=await fetch(e,{method:"GET",headers:{Authorization:"Bearer ".concat(n),Accept:"application/octet-stream"}});if(!t.ok)throw Error("HTTP ".concat(t.status,": ").concat(t.statusText));let r=await t.blob(),o=URL.createObjectURL(r),s=document.createElement("a");s.href=o,s.download=l,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(o),console.log("[TASK-CENTER] Secure download completed successfully")}else throw console.error("[TASK-CENTER] No download URL provided"),Error("Download URL not available")}catch(e){console.error("[TASK-CENTER] Download failed:",e)}},[]),N=(0,a.useCallback)(()=>{m([]),h([])},[h]);return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("button",{onClick:()=>c(!0),className:"relative overflow-hidden px-4 py-2 text-sm font-semibold rounded-2xl transition-all duration-300 flex items-center gap-2 shadow-lg hover:shadow-xl transform group text-white bg-gradient-to-r from-purple-500 via-purple-600 to-indigo-600 hover:from-purple-600 hover:via-purple-700 hover:to-indigo-700 hover:scale-105 backdrop-blur-sm ".concat(t),children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-purple-400/20 via-purple-500/20 to-indigo-500/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsx)(eg.A,{className:"w-4 h-4 relative z-10"}),(0,r.jsx)("span",{className:"relative z-10",children:"任务中心"}),l.length>0&&(0,r.jsx)("span",{className:"relative z-10 bg-red-500 text-white text-xs rounded-full px-1.5 py-0.5 min-w-[18px] h-[18px] flex items-center justify-center",children:l.length>99?"99+":l.length})]}),(0,r.jsx)(H.lG,{open:s,onOpenChange:c,children:(0,r.jsxs)(H.Cf,{className:"max-w-4xl max-h-[80vh] flex flex-col",children:[(0,r.jsx)(H.c7,{children:(0,r.jsxs)(H.L3,{className:"flex items-center gap-2 text-xl font-bold",children:[(0,r.jsx)(eg.A,{className:"w-5 h-5 text-purple-600"}),"任务中心",(0,r.jsxs)("span",{className:"text-sm font-normal text-gray-500",children:["(",l.length," 个任务)"]})]})}),(0,r.jsxs)("div",{className:"flex items-center gap-3 py-4 border-b border-gray-100",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[(0,r.jsx)(T.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),(0,r.jsx)(g.p,{placeholder:"搜索任务ID...",value:d,onChange:e=>u(e.target.value),className:"pl-10 h-10 border-2 border-gray-200 focus:border-purple-400 focus:ring-2 focus:ring-purple-50"})]}),(0,r.jsxs)(i.$,{onClick:N,variant:"outline",size:"sm",className:"h-10 px-4 border-2 border-red-200 text-red-600 hover:bg-red-50 hover:border-red-300",children:[(0,r.jsx)(E.A,{className:"w-4 h-4 mr-1"}),"清空"]})]}),(0,r.jsx)("div",{className:"flex-1 overflow-y-auto",children:0===p.length?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center h-64 text-gray-500",children:[(0,r.jsx)(eg.A,{className:"w-12 h-12 mb-4 text-gray-300"}),(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"暂无任务"}),(0,r.jsx)("p",{className:"text-sm text-center max-w-sm",children:d?"没有找到匹配的任务":"您还没有创建任何任务，开始使用AI语音转换功能来创建您的第一个任务吧！"})]}):(0,r.jsx)("div",{className:"space-y-3 p-1",children:p.map((e,n)=>(0,r.jsx)(ek,{task:e,index:n,onCopy:k,onDownload:j,onRefresh:v,copiedTaskId:f},e.taskId))})})]})})]})});eh.displayName="TaskCenter";let ey=e=>{let n=new Date(e),t=Math.floor((new Date().getTime()-n.getTime())/1e3);if(t<60)return"刚刚";if(t<3600){let e=Math.floor(t/60);return"".concat(e,"分钟前")}if(t<86400){let e=Math.floor(t/3600);return"".concat(e,"小时前")}{let e=Math.floor(t/86400);return"".concat(e,"天前")}},ew=e=>{switch(e){case"processing":return"bg-orange-100 text-orange-600";case"complete":return"bg-green-100 text-green-600";case"failed":return"bg-red-100 text-red-600";default:return"bg-gray-100 text-gray-600"}},ev=e=>{switch(e){case"processing":return"处理中";case"complete":return"已完成";case"failed":return"失败";default:return"未知"}},ek=e=>{let{task:n,index:t,onCopy:o,onDownload:s,onRefresh:a,copiedTaskId:i}=e;return(0,r.jsx)("div",{className:"bg-white border-2 border-gray-100 rounded-xl p-4 hover:border-purple-200 hover:shadow-md transition-all duration-200",children:(0,r.jsxs)("div",{className:"flex items-start justify-between",children:[(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-2",children:[(0,r.jsxs)("span",{className:"text-xs font-medium text-purple-600 bg-purple-100 px-2 py-1 rounded-lg",children:["#",t+1]}),(0,r.jsxs)("span",{className:"text-xs text-gray-500 flex items-center gap-1",children:[(0,r.jsx)(_.A,{className:"w-3 h-3"}),ey(n.createdAt)]}),n.status&&(0,r.jsx)("span",{className:"text-xs px-2 py-1 rounded-lg font-medium ".concat(ew(n.status)),children:ev(n.status)})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-3",children:[(0,r.jsx)("span",{className:"text-sm font-mono text-gray-700 bg-gray-50 px-2 py-1 rounded border truncate max-w-[700px]",children:n.taskId}),(0,r.jsx)("button",{onClick:()=>o(n.taskId),className:"p-1 rounded hover:bg-gray-100 transition-colors duration-200",title:"复制任务ID",children:i===n.taskId?(0,r.jsx)(ef.A,{className:"w-3 h-3 text-green-600"}):(0,r.jsx)(B.A,{className:"w-3 h-3 text-gray-400"})})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 ml-4",children:["complete"===n.status&&n.downloadUrl&&(0,r.jsx)("button",{onClick:()=>s(n.downloadUrl,n.taskId),className:"p-2 rounded-lg bg-green-100 text-green-600 hover:bg-green-200 hover:text-green-700 transition-all duration-200",title:"下载音频",children:(0,r.jsx)(S.A,{className:"w-4 h-4"})}),(0,r.jsx)("button",{onClick:()=>a(n.taskId),disabled:n.isRefreshing,className:"p-2 rounded-lg transition-all duration-200 flex items-center justify-center ".concat(n.isRefreshing?"bg-gray-100 text-gray-400 cursor-not-allowed":"bg-blue-100 text-blue-600 hover:bg-blue-200 hover:text-blue-700 cursor-pointer"),title:"刷新任务状态",children:(0,r.jsx)(ep.A,{className:"w-4 h-4 ".concat(n.isRefreshing?"animate-spin":"")})})]})]})})},ej=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("span",{className:"text-white-600 font-bold text-lg",children:"♂"})})},eN=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("span",{className:"text-white-600 font-bold text-lg",children:"♀"})})},ez=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("span",{className:"text-gray-600 font-bold text-lg",children:"⚲"})})},eA=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("img",{src:"https://img.icons8.com/3d-fluency/94/person-male--v3.png",alt:"Male",className:"w-4 h-4 object-cover"})})},eC=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("img",{src:"https://img.icons8.com/3d-fluency/94/person-female--v3.png",alt:"Female",className:"w-4 h-4 object-cover"})})},eS=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("img",{src:"https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/us.svg",alt:"US Flag",className:"w-4 h-3 object-cover rounded-sm"})})},eT=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("img",{src:"https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/jp.svg",alt:"JP Flag",className:"w-4 h-3 object-cover rounded-sm"})})},eE=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("img",{src:"https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/es.svg",alt:"ES Flag",className:"w-4 h-3 object-cover rounded-sm"})})},eI=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("img",{src:"https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/kr.svg",alt:"KR Flag",className:"w-4 h-3 object-cover rounded-sm"})})},eL=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("img",{src:"https://tts-1256990318.cos.ap-guangzhou.myqcloud.com/svg/fr.svg",alt:"FR Flag",className:"w-4 h-3 object-cover rounded-sm"})})},eD=e=>{let{className:n}=e;return(0,r.jsx)("div",{className:"flex items-center justify-center ".concat(n),children:(0,r.jsx)("img",{src:"https://img.icons8.com/pulsar-gradient/48/infinity.png",alt:"Infinity",className:"w-4 h-4 object-contain"})})},eR=e=>{let{value:n,onChange:t,options:o,className:s="",hoverColor:i="green"}=e,[c,l]=(0,a.useState)(!1),m=(0,a.useRef)(null),d=o.find(e=>e.value===n);(0,a.useEffect)(()=>{let e=e=>{m.current&&!m.current.contains(e.target)&&l(!1)};return document.addEventListener("mousedown",e),()=>document.removeEventListener("mousedown",e)},[]);let u={green:"group-hover:text-green-500 focus:border-green-400 focus:ring-green-500/10 hover:border-green-300",orange:"group-hover:text-orange-500 focus:border-orange-400 focus:ring-orange-500/10 hover:border-orange-300"};return(0,r.jsxs)("div",{className:"relative group",ref:m,children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r ".concat({green:"from-green-500/5 to-blue-500/5",orange:"from-orange-500/5 to-red-500/5"}[i]," rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300")}),(0,r.jsxs)("button",{onClick:()=>l(!c),className:"relative w-full px-4 py-3 text-sm border-2 border-gray-200/60 rounded-2xl bg-white/90 backdrop-blur-xl hover:border-gray-300 ".concat(u[i]," transition-all duration-300 outline-none cursor-pointer shadow-sm hover:shadow-lg font-medium text-gray-700 flex items-center justify-between"),children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("span",{className:"text-gray-400 ".concat(u[i]," transition-colors duration-300"),children:null==d?void 0:d.icon}),(0,r.jsx)("span",{children:null==d?void 0:d.label})]}),(0,r.jsx)(f.A,{className:"w-4 h-4 text-gray-400 ".concat(u[i]," transition-all duration-300 ").concat(c?"rotate-180":"")})]}),c&&(0,r.jsx)("div",{className:"absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-2xl shadow-3xl z-50 overflow-hidden",children:o.map(e=>(0,r.jsxs)("button",{onClick:()=>{t(e.value),l(!1)},className:"w-full px-4 py-3 text-sm text-left hover:bg-gray-50 transition-all duration-200 flex items-center gap-3 ".concat(n===e.value?"bg-blue-50 text-blue-700":"text-gray-700"),children:[(0,r.jsx)("span",{className:"".concat(n===e.value?"text-blue-600":"text-gray-400"," transition-colors duration-200"),children:e.icon}),(0,r.jsx)("span",{children:e.label})]},e.value))})]})};function e_(){var e,n,t,o;let{voices:l,isLoading:m,error:u,voiceIconMapping:p,retry:B}=function(){var e;let[n,t]=(0,a.useState)({voices:[],isLoading:!0,error:null,voiceIconMapping:{}}),[r,o]=(0,a.useState)(!1);(0,a.useEffect)(()=>{o(!0)},[]);let s=(0,a.useCallback)(()=>"https://my-tts-config-1256990318.cos.ap-guangzhou.myqcloud.com/voices.json",[]),i=(0,a.useCallback)(e=>{if(!r)return{};let n=function(e){let n=[...e];for(let e=n.length-1;e>0;e--){let t=Math.floor(Math.random()*(e+1));[n[e],n[t]]=[n[t],n[e]]}return n}(ec),t={};return e.forEach((e,r)=>{t[e.id]=n[r%n.length]}),t},[r]),c=(0,a.useCallback)(async()=>{try{t(e=>({...e,isLoading:!0,error:null}));let e=s(),n=await fetch(e,{cache:"no-cache"});if(!n.ok)throw el("network","无法获取声音列表: ".concat(n.status," ").concat(n.statusText),!0);let r=await n.json();if(!Array.isArray(r))throw el("parse","声音数据格式错误：期望数组格式",!0);if(0===r.length)throw el("empty","声音列表为空，请联系管理员",!0);let o=r.filter(e=>!e.id||!e.name||!e.gender||!e.description);o.length>0&&console.warn("发现无效的声音数据:",o);let a=i(r);t({voices:r,isLoading:!1,error:null,voiceIconMapping:a})}catch(n){let e;console.error("获取声音数据失败:",n),e=n instanceof TypeError&&n.message.includes("fetch")?el("network","网络连接失败，请检查网络后重试",!0):n.type?n:el("unknown",n.message||"加载声音配置时发生未知错误",!0),t(n=>({...n,isLoading:!1,error:e}))}},[s,i]),l=(0,a.useCallback)(()=>{var e;(null===(e=n.error)||void 0===e?void 0:e.retryable)&&c()},[null===(e=n.error)||void 0===e?void 0:e.retryable,c]);return(0,a.useEffect)(()=>{c()},[c]),(0,a.useEffect)(()=>{if(r&&n.voices.length>0&&0===Object.keys(n.voiceIconMapping).length){let e=i(n.voices);t(n=>({...n,voiceIconMapping:e}))}},[r,n.voices,n.voiceIconMapping,i]),{...n,retry:l,refetch:c}}(),W=["https://eleven-public-cdn.elevenlabs.io/payloadcms/ox7fne3bkeo-brian.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/twhwqss70ic-alice.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/4vvqikmli2m-bill.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/gwb2kbm395-callum.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/ixo4og3542i-charlie.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/zw9ec3ktkch-charlotte.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/xu3c1krvtn-chris.jpg","https://eleven-public-cdn.elevenlabs.io/payloadcms/toavylo6g7-daniel.jpg"],[K,G]=(0,a.useState)(""),[V,$]=(0,a.useState)(""),[Y,J]=(0,a.useState)(!1),[Z,X]=(0,a.useState)(!1),[Q,ee]=(0,a.useState)(!1),[em,ed]=(0,a.useState)([.58]),[eu,ep]=(0,a.useState)([.75]),[eg,ef]=(0,a.useState)([.3]),[ex,ey]=(0,a.useState)([1]),[ew,ev]=(0,a.useState)(!1),[ek,e_]=(0,a.useState)(!1),[eP,eM]=(0,a.useState)("eleven_multilingual_v2"),[eU,eO]=(0,a.useState)(!1),[eF,eB]=(0,a.useState)(!1),[eW,eK]=(0,a.useState)(""),[eG,eH]=(0,a.useState)(!1),[eV,e$]=(0,a.useState)(null),[eq,eY]=(0,a.useState)({streamUrl:null,downloadUrl:null,secureStreamUrl:null}),[eJ,eZ]=(0,a.useState)("00:00"),[eX,eQ]=(0,a.useState)("00:00"),[e0,e5]=(0,a.useState)(0),[e2,e1]=(0,a.useState)(0),[e3,e6]=(0,a.useState)(!1),[e7,e9]=(0,a.useState)(null),[e4,e8]=(0,a.useState)(!1),[ne,nn]=(0,a.useState)(!1),[nt,nr]=(0,a.useState)(null),[no,ns]=(0,a.useState)(null),[na,ni]=(0,a.useState)(!1),{toast:nc}=(0,eo.dj)(),nl=async e=>{try{let n=er.tC.getAccessToken();if(!n)return console.error("[SECURE-AUDIO] No token available for audio loading"),null;console.log("[SECURE-AUDIO] Loading audio with Authorization header:",e);let t=await fetch(e,{method:"GET",headers:{Authorization:"Bearer ".concat(n),Accept:"audio/mpeg"}});if(!t.ok)throw Error("HTTP ".concat(t.status,": ").concat(t.statusText));let r=await t.blob(),o=URL.createObjectURL(r);return console.log("[SECURE-AUDIO] Audio loaded successfully, blob URL created"),o}catch(e){return console.error("[SECURE-AUDIO] Failed to load audio:",e),null}},nm=async(e,n)=>{try{let t=er.tC.getAccessToken();if(!t)return console.error("[SECURE-DOWNLOAD] No token available for download"),e9("认证失败，请重新登录"),!1;console.log("[SECURE-DOWNLOAD] Downloading audio with Authorization header:",e);let r=await fetch(e,{method:"GET",headers:{Authorization:"Bearer ".concat(t),Accept:"application/octet-stream"}});if(!r.ok)throw Error("HTTP ".concat(r.status,": ").concat(r.statusText));let o=await r.blob(),s=URL.createObjectURL(o),a=document.createElement("a");return a.href=s,a.download=n,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s),console.log("[SECURE-DOWNLOAD] Download completed successfully"),!0}catch(e){return console.error("[SECURE-DOWNLOAD] Download failed:",e),e9("下载失败，请检查网络连接或重试"),!1}},[nd,nu]=(0,a.useState)(!1),[np,nb]=(0,a.useState)(!1),[ng,nf]=(0,a.useState)(""),[nx,nh]=(0,a.useState)({isVip:!1,expireAt:0}),[ny,nw]=(0,a.useState)(""),[nv,nk]=(0,a.useState)(""),[nj,nN]=(0,a.useState)("all"),[nz,nA]=(0,a.useState)("all"),[nC,nS]=(0,a.useState)(!1),[nT,nE]=(0,a.useState)("all"),[nI,nL]=(0,a.useState)("all"),[nD,nR]=(0,a.useState)(""),[n_,nP]=(0,a.useState)("");(0,a.useEffect)(()=>{let e=setTimeout(()=>{nk(ny)},300);return()=>clearTimeout(e)},[ny]);let[nM,nU]=(0,a.useState)(null),[nO,nF]=(0,a.useState)("idle"),[nB,nW]=(0,a.useState)(""),[nK,nG]=(0,a.useState)(null),[nH,nV]=(0,a.useState)([]),n$=(0,a.useRef)(null),nq="tts_task_mapping",[nY,nJ]=(0,a.useState)(0),[nZ,nX]=(0,a.useState)([]),[nQ,n0]=(0,a.useState)(null),n5=(0,a.useRef)(null),n2="tts_retry_task_data",[n1,n3]=(0,a.useState)({frontendAttempts:0,frontendMaxAttempts:1,frontendInProgress:!1,frontendStartTime:0,datacenterAttempts:0,datacenterMaxAttempts:1,backupApiAttempts:0,backupApiMaxAttempts:2,usingBackupApi:!1,backupApiStartTime:0,currentBackupIndex:-1,maxBackupIndex:-1,isLocked:!1,lockReason:"",activeRetryType:null,errorHistory:[]}),n6=(0,a.useRef)(nO);(0,a.useEffect)(()=>{n6.current=nO},[nO]);let[n7,n9]=(0,a.useState)(!1),[n4,n8]=(0,a.useState)({currentPassword:"",newPassword:"",confirmPassword:""}),[te,tn]=(0,a.useState)(!1),[tt,tr]=(0,a.useState)(!1),[to,ts]=(0,a.useState)(!1),[ta,ti]=(0,a.useState)(""),[tc,tl]=(0,a.useState)(!1),[tm,td]=(0,a.useState)(!1),[tu,tp]=(0,a.useState)(!1),[tb,tg]=(0,a.useState)(!1),[tf,tx]=(0,a.useState)(!1),[th,ty]=(0,a.useState)(null),[tw,tv]=(0,a.useState)("single"),[tk,tj]=(0,a.useState)([{id:1,voice:"",text:""}]),[tN,tz]=(0,a.useState)(null),[tA,tC]=(0,a.useState)(null),[tS,tT]=(0,a.useState)(!1),tE=(0,a.useRef)(null),tI=(0,a.useRef)(null),tL=(0,a.useRef)(null),tD=(0,a.useRef)(null),tR=(0,a.useRef)(null);(0,a.useRef)(null);let t_=(0,a.useRef)(null),tP=(0,a.useRef)(e3),tM=(0,a.useRef)(!1),tU=(0,a.useRef)(null),tO=(0,a.useRef)(null),tF=(0,a.useMemo)(()=>"eleven_v3"===eP?3e3:5e3,[eP]),tB=(0,a.useMemo)(()=>{let e=l;if("all"!==nj&&(e=e.filter(e=>e.gender===nj)),"all"!==nz&&(e=e.filter(e=>e.language===nz)),nv.trim()){let n=nv.toLowerCase().trim();e=e.filter(e=>e.name.toLowerCase().includes(n)||e.description.toLowerCase().includes(n))}return e},[l,nj,nz,nv]),tW=(0,a.useMemo)(()=>{let e=l;if("all"!==nT&&(e=e.filter(e=>e.gender===nT)),"all"!==nI&&(e=e.filter(e=>e.language===nI)),nD.trim()){let n=nD.toLowerCase().trim();e=e.filter(e=>e.name.toLowerCase().includes(n)||e.description.toLowerCase().includes(n))}return e},[l,nT,nI,nD]),tK=(0,a.useMemo)(()=>{let e=(K.match(/\[([a-zA-Z\s]+)\]/g)||[]).length;return 0===e?null:(0,r.jsxs)("div",{className:"absolute -top-8 right-2 bg-purple-100 text-purple-700 text-sm font-semibold px-2.5 py-1 rounded-lg z-10",children:["情感标注词 ",e,"个"]})},[K]),tG=()=>{if(nN(nT),nA(nI),nw(nD),n_)$(n_);else{let e=l.filter(e=>{if("all"!==nT&&e.gender!==nT||"all"!==nI&&e.language!==nI)return!1;if(nD.trim()){let n=nD.toLowerCase().trim();if(!e.name.toLowerCase().includes(n)&&!e.description.toLowerCase().includes(n))return!1}return!0});e.length>0&&!e.find(e=>e.id===V)&&$(e[0].id)}nS(!1)},tH=[{id:"eleven_v3",name:"Eleven v3 (测试)支持情感标注慢速",description:"最具表现力的模型，支持70多种语言。相比以往的模型，需要更多的提示工程。目前处于 Alpha 阶段，可靠性将随着时间推移不断提升",icon:(0,r.jsxs)("svg",{width:"33",height:"33",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"transition-all duration-200 ease-in-out",children:[(0,r.jsx)("rect",{x:"0",y:"0",width:"32",height:"32",rx:"16",fill:"#5D79DF",stroke:"#F3F4F6",strokeWidth:"2.5",className:"group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"}),(0,r.jsx)("path",{d:"M16.7527 21.9441C16.6291 22.2951 16.1328 22.2951 16.0093 21.9441L14.9179 18.8442C14.8458 18.6395 14.6219 18.5316 14.417 18.6027L11.4214 19.642C11.0768 19.7615 10.7687 19.3892 10.9506 19.0731L12.6191 16.1737C12.7216 15.9955 12.6699 15.7684 12.5003 15.6521L9.7946 13.7969C9.4974 13.5931 9.6069 13.1303 9.9639 13.0814L13.1369 12.6469C13.3456 12.6183 13.4949 12.4307 13.4759 12.2209L13.1743 8.8874C13.141 8.519 13.5876 8.3118 13.8474 8.5751L16.1004 10.8595C16.2547 11.016 16.5072 11.016 16.6616 10.8595L18.9146 8.5751C19.1743 8.3118 19.621 8.519 19.5877 8.8874L19.2861 12.2209C19.2671 12.4307 19.4164 12.6183 19.6251 12.6469L22.798 13.0814C23.155 13.1303 23.2646 13.5931 22.9674 13.7969L20.2616 15.6521C20.0921 15.7684 20.0404 15.9955 20.1429 16.1737L21.8113 19.0731C21.9932 19.3892 21.6852 19.7615 21.3406 19.642L18.345 18.6027C18.14 18.5316 17.9161 18.6395 17.8441 18.8442L16.7527 21.9441Z",stroke:"white",strokeWidth:"1.5",fill:"none"})]})},{id:"eleven_multilingual_v2",name:"Eleven Multilingual v2",description:"最具真实感、情感丰富的模式，支持29种语言。非常适合配音、有声书、后期制作或其他内容创作需求",icon:(0,r.jsxs)("svg",{width:"33",height:"33",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"transition-all duration-200 ease-in-out",children:[(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:"multilingual-gradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#F99BFF"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#9B59FF"})]})}),(0,r.jsx)("rect",{x:"0",y:"0",width:"32",height:"32",rx:"16",fill:"url(#multilingual-gradient)",stroke:"#F3F4F6",strokeWidth:"2.5",className:"group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"}),(0,r.jsx)("path",{d:"M12 10C13.5 10 14.5 11 14.5 12.5V13.5C14.5 14 14.8 14.3 15.3 14.3C15.8 14.3 16.1 14 16.1 13.5V12C16.1 10.3 14.8 9 13.1 9C11.4 9 10.1 10.3 10.1 12V16C10.1 17.7 11.4 19 13.1 19H13.5",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M13.5 16.5C13.8 16.8 14.2 17 14.7 17",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M18 12C18.5 12.5 18.8 13.2 18.8 14C18.8 14.8 18.5 15.5 18 16",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M20 10C21 11 21.5 12.4 21.5 14C21.5 15.6 21 17 20 18",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"}),(0,r.jsx)("path",{d:"M22 8C23.5 9.5 24.2 11.6 24.2 14C24.2 16.4 23.5 18.5 22 20",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round",strokeLinejoin:"round"})]})},{id:"eleven_turbo_v2_5",name:"Eleven Turbo v2.5",description:"高质量、低延迟模型支持32种语言。非常适合在需要速度且需要使用非英语语言的使用场景",icon:(0,r.jsxs)("svg",{width:"33",height:"33",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"transition-all duration-200 ease-in-out",children:[(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:"turbo-gradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#10B981"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#059669"})]})}),(0,r.jsx)("rect",{x:"0",y:"0",width:"32",height:"32",rx:"16",fill:"url(#turbo-gradient)",stroke:"#F3F4F6",strokeWidth:"2.5",className:"group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"}),(0,r.jsx)("path",{d:"M18 8L12 16H16L14 24L20 16H16L18 8Z",fill:"white"}),(0,r.jsx)("path",{d:"M22 10H26",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round"}),(0,r.jsx)("path",{d:"M23 13H25",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round"}),(0,r.jsx)("path",{d:"M22 19H26",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round"}),(0,r.jsx)("path",{d:"M23 22H25",stroke:"white",strokeWidth:"1.5",strokeLinecap:"round"})]})},{id:"eleven_turbo_v2",name:"Eleven Turbo v2",description:"仅支持英文的低延迟模型，在需要速度且仅需英文的情况下表现最佳。性能与 Turbo v2.5 相当",icon:(0,r.jsxs)("svg",{width:"33",height:"33",viewBox:"0 0 32 32",fill:"none",xmlns:"http://www.w3.org/2000/svg",className:"transition-all duration-200 ease-in-out",children:[(0,r.jsx)("defs",{children:(0,r.jsxs)("linearGradient",{id:"turbo-v2-gradient",x1:"0%",y1:"0%",x2:"100%",y2:"100%",children:[(0,r.jsx)("stop",{offset:"0%",stopColor:"#3B82F6"}),(0,r.jsx)("stop",{offset:"100%",stopColor:"#1D4ED8"})]})}),(0,r.jsx)("rect",{x:"0",y:"0",width:"32",height:"32",rx:"16",fill:"url(#turbo-v2-gradient)",stroke:"#F3F4F6",strokeWidth:"2.5",className:"group-hover:stroke-gray-300 transition-all duration-200 ease-in-out"}),(0,r.jsx)("path",{d:"M16 6L18 12H20L16 26L12 12H14L16 6Z",fill:"white"}),(0,r.jsx)("path",{d:"M14 12C13.5 13 13.5 14 14 15",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round"}),(0,r.jsx)("path",{d:"M18 12C18.5 13 18.5 14 18 15",stroke:"white",strokeWidth:"1.2",strokeLinecap:"round"}),(0,r.jsx)("text",{x:"16",y:"21",textAnchor:"middle",fill:"white",fontSize:"6",fontWeight:"bold",fontFamily:"Arial, sans-serif",children:"EN"})]})}],tV=(0,a.useMemo)(()=>"dialogue"===tw?tH.filter(e=>"eleven_v3"===e.id):tH,[tw]);(0,a.useEffect)(()=>{tP.current=e3},[e3]),(0,a.useEffect)(()=>{ni(!0)},[]),(0,a.useEffect)(()=>{if(l.length>0&&!V){let e=l[0].id;$(e),tj(n=>n.map(n=>""===n.voice?{...n,voice:e}:n))}},[l,V]),(0,a.useEffect)(()=>{let e=et.j2.isLoggedIn(),n=et.j2.getCurrentUserEmail();if(!e){window.location.href="/login";return}eK(n||""),eB(!0),(async()=>{try{let e=await et.j2.getUserQuota();nh({isVip:e.isVip,type:e.type,expireAt:e.expireAt})}catch(t){console.error("获取用户状态失败:",t);let{isAuthError:e,shouldRedirect:n}=(0,es.tu)(t,()=>{nn(!0)});e&&n&&setTimeout(()=>{window.location.href="/login"},2e3)}})();let t=setTimeout(()=>{tp(!0)},1e3),r=e=>{tE.current&&!tE.current.contains(e.target)&&(e_(!1),tC(null)),tD.current&&!tD.current.contains(e.target)&&eO(!1),tR.current&&!tR.current.contains(e.target)&&eH(!1)};return document.addEventListener("mousedown",r),()=>{document.removeEventListener("mousedown",r),clearTimeout(t)}},[]),(0,a.useEffect)(()=>{let e=t_.current;if(!e||!eq.secureStreamUrl)return;let n=async()=>{let n=e.duration;if(n&&!isNaN(n)){let e=Math.floor(n/60),t=Math.floor(n%60);eZ("".concat(e.toString().padStart(2,"0"),":").concat(t.toString().padStart(2,"0")))}if(tM.current)try{await e.play(),ee(!0),tM.current=!1}catch(e){console.error("Autoplay failed:",e),ee(!1),tM.current=!1}},t=()=>{if(e&&!tP.current){let n=e.currentTime,t=e.duration,r=Math.floor(n/60),o=Math.floor(n%60);eQ("".concat(r.toString().padStart(2,"0"),":").concat(o.toString().padStart(2,"0"))),t>0&&e1(n/t*100)}},r=e=>{var n;console.error("[AUDIO] Failed to load audio:",e),console.error("[AUDIO] Secure Stream URL:",eq.secureStreamUrl),console.error("[AUDIO] Audio error details:",null===(n=e.target)||void 0===n?void 0:n.error),ee(!1),X(!1),tM.current=!1,e9("音频加载失败，请检查网络连接或重新生成")};return e.addEventListener("loadedmetadata",n),e.addEventListener("timeupdate",t),e.addEventListener("error",r),()=>{e.removeEventListener("loadedmetadata",n),e.removeEventListener("timeupdate",t),e.removeEventListener("error",r)}},[eq.secureStreamUrl]),(0,a.useEffect)(()=>()=>{nt&&(nt.pause(),nt.currentTime=0),eq.secureStreamUrl&&eq.secureStreamUrl.startsWith("blob:")&&URL.revokeObjectURL(eq.secureStreamUrl)},[nt,eq.secureStreamUrl]),(0,a.useEffect)(()=>{let e=()=>{if(ek&&tE.current){let e=tE.current.getBoundingClientRect(),n=window.innerHeight;window.innerWidth,e.bottom+400>n&&(e_(!1),setTimeout(()=>e_(!0),10))}},n=()=>e(),t=()=>e();if(ek)return window.addEventListener("scroll",n,{passive:!0}),window.addEventListener("resize",t,{passive:!0}),()=>{window.removeEventListener("scroll",n),window.removeEventListener("resize",t)}},[ek]),(0,a.useEffect)(()=>{ek&&tL.current&&setTimeout(()=>{var e;null===(e=tL.current)||void 0===e||e.focus()},100)},[ek]),(0,a.useEffect)(()=>{ek&&V&&tI.current&&setTimeout(()=>{var e;let n=null===(e=tI.current)||void 0===e?void 0:e.querySelector('[data-voice-id="'.concat(V,'"]'));n&&n.scrollIntoView({behavior:"smooth",block:"center"})},50)},[ek,V,tB]),(0,a.useEffect)(()=>{"dialogue"===tw&&"eleven_v3"!==eP&&eM("eleven_v3")},[tw,eP]),(0,a.useEffect)(()=>{if("eleven_v3"===eP){let e;let n=em[0];e=n<=.25?0:n<=.75?.5:1,n!==e&&ed([e])}},[eP,em]);let t$=(0,a.useRef)(eP);(0,a.useEffect)(()=>{if(t$.current!==eP){let e="eleven_v3"===eP?3e3:5e3;if(K.length>e){let n=K.length;G(K.substring(0,e));let t="eleven_v3"===eP?"Eleven v3":"其他模型";nf("切换到".concat(t,"模型，字符限制为").concat(e,"字符，已自动截取原文本（").concat(n,"字符）到").concat(e,"字符")),nb(!0),setTimeout(()=>{nb(!1)},3e3)}t$.current=eP}},[eP,K]);let tq=(0,a.useMemo)(()=>!!Y||!!n1.frontendInProgress||("single"===tw?!K.trim()||!V:"dialogue"!==tw||tk.some(e=>!e.text.trim()||!e.voice)),[tw,Y,n1.frontendInProgress,K,V,tk]);(0,a.useEffect)(()=>{th&&ty(null)},[K]),(0,a.useEffect)(()=>{null!==tN&&V&&tj(e=>{let n=e.find(e=>e.id===tN);return n&&n.voice!==V?e.map(e=>e.id===tN?{...e,voice:V}:e):e}),null!==tA&&V&&tj(e=>{let n=e.find(e=>e.id===tA);return n&&n.voice!==V?e.map(e=>e.id===tA?{...e,voice:V}:e):e})},[V,tN,tA]),(0,a.useEffect)(()=>{if(null!==tN){let e=tk.find(e=>e.id===tN);e&&e.voice!==V&&$(e.voice)}},[tN]);let tY=async e=>{tv(e)},tJ=(e,n,t)=>{tj(tk.map(r=>r.id===e?{...r,[n]:t}:r))},tZ=async(e,n)=>{try{if("replace"===n)tj(e),e.length>0&&($(e[0].voice),tz(e[0].id));else{let n=[...tk,...e];tj(n),e.length>0&&($(e[0].voice),tz(e[0].id),setTimeout(()=>{let n=document.querySelector('[data-dialogue-line-id="'.concat(e[0].id,'"]'));n&&n.closest(".overflow-y-auto")&&n.scrollIntoView({behavior:"smooth",block:"center"})},100))}}catch(e){throw console.error("批量导入失败:",e),e}},tX=async()=>{try{await et.j2.logout(),window.location.href="/login"}catch(e){console.error("Logout error:",e),window.location.href="/login"}},tQ=async()=>{if(!K.trim()){ty("请先输入要处理的文本");return}if(!tf){if(!et.j2.isLoggedIn()){ty("请先登录后使用自动标注功能"),nn(!0);return}if(!nx.isVip){ty("自动标注功能需要会员权限，请先开通会员");return}if(Date.now()>nx.expireAt){ty("会员已过期，请续费后使用自动标注功能");return}tx(!0),ty(null);try{let e=await et.lY.processText(K.trim(),"auto");if(!e.success||!e.processedText)throw Error("服务返回的数据格式不正确");G(e.processedText),e.rateLimit&&console.log("自动标注剩余次数: ".concat(e.rateLimit.remaining))}catch(n){console.error("Auto generate tags error:",n);let e="自动生成标签失败，请稍后重试";"TypeError"===n.name&&n.message.includes("fetch")?e="网络连接失败，请检查网络后重试":n.message.includes("登录")?(e=n.message,nn(!0)):n.message.includes("会员")||n.message.includes("权限")?e=n.message:n.message.includes("频繁")?e=n.message:n.message&&(e=n.message),ty(e)}finally{tx(!1)}}},t0=(0,a.useCallback)((e,n)=>{nf("输入内容超过".concat(n,"字符限制，原内容").concat(e,"字符，已自动截取到").concat(n,"字符")),nb(!0),setTimeout(()=>{nb(!1)},3e3)},[]);if(u)return(0,r.jsx)(eb,{error:u,onRetry:B});let t5=async()=>{if(ti(""),!n4.currentPassword||!n4.newPassword||!n4.confirmPassword){ti("请填写所有密码字段");return}if(n4.newPassword!==n4.confirmPassword){ti("新密码和确认密码不匹配");return}if(n4.newPassword.length<6){ti("新密码长度不能少于6位");return}if(n4.currentPassword===n4.newPassword){ti("新密码不能与当前密码相同");return}tl(!0);try{await et.j2.changePassword({currentPassword:n4.currentPassword,newPassword:n4.newPassword}),n9(!1),n8({currentPassword:"",newPassword:"",confirmPassword:""}),ti(""),td(!0)}catch(e){console.error("Change password error:",e),ti(e.message||"修改密码失败，请重试")}finally{tl(!1)}},t2=(0,a.useCallback)(e=>{n0(e),n5.current=e;try{e?sessionStorage.setItem(n2,JSON.stringify(e)):sessionStorage.removeItem(n2)}catch(e){console.error("[TASK-DATA-SAFE] Failed to save to sessionStorage:",e)}},[n2]),t1=(0,a.useCallback)(()=>{if(nQ)return nQ;if(n5.current)return n0(n5.current),n5.current;try{let e=sessionStorage.getItem(n2);if(e){let n=JSON.parse(e);return n0(n),n5.current=n,n}}catch(e){console.error("[TASK-DATA-SAFE] Failed to recover from sessionStorage:",e)}return null},[nQ,n2]),t3=(0,a.useCallback)(e=>{n3(n=>{let t=e(n);try{sessionStorage.setItem("retry_state",JSON.stringify({frontendAttempts:t.frontendAttempts,datacenterAttempts:t.datacenterAttempts,frontendStartTime:t.frontendStartTime,errorHistory:t.errorHistory.slice(-10)}))}catch(e){console.error("[RETRY-STATE] Failed to persist retry state:",e)}return t})},[]),t6=(0,a.useCallback)(()=>{try{let e=sessionStorage.getItem("retry_state");if(e){let n=JSON.parse(e);t3(e=>({...e,frontendAttempts:n.frontendAttempts||0,datacenterAttempts:n.datacenterAttempts||0,frontendStartTime:n.frontendStartTime||0,errorHistory:n.errorHistory||[]}))}}catch(e){console.error("[RETRY-STATE] Failed to recover retry state:",e)}},[t3]),t7=(0,a.useCallback)(()=>{try{let e=sessionStorage.getItem(nq);if(e){let n=JSON.parse(e);if(Date.now()-n.createdAt<36e5)return n}return sessionStorage.removeItem(nq),null}catch(e){return console.error("[TASK-MAPPING] Failed to get task mapping:",e),null}},[]),t9=(0,a.useCallback)(e=>{try{sessionStorage.setItem(nq,JSON.stringify(e)),console.log("[TASK-MAPPING] Saved mapping to sessionStorage:",e)}catch(e){console.error("[TASK-MAPPING] Failed to save task mapping:",e)}},[]);(0,a.useEffect)(()=>{let e=t7();e&&(console.log("[TASK-MAPPING] Recovered mapping from sessionStorage:",e),n$.current=e.logicalTaskId,nG(e.logicalTaskId))},[t7]);let t4=(0,a.useCallback)(e=>{let n=t7();return n&&n.displayTaskId===e?(console.log("[TASK-MAPPING] Found mapping for refresh:",{displayTaskId:e,physicalTaskIds:n.physicalTaskIds}),n.physicalTaskIds):(console.log("[TASK-MAPPING] No mapping found for refresh, using original ID:",e),[e])},[t7]);(0,a.useEffect)(()=>{t6()},[t6]),(0,a.useEffect)(()=>{if(Z&&eq.downloadUrl&&"complete"===nO){console.log("[STATUS-SYNC] Detected audio generation completed, syncing task center status");let e=t7();e&&tU.current&&(tU.current.updateTaskStatus(e.displayTaskId,"complete",eq.downloadUrl),console.log("[STATUS-SYNC] Updated task center via mapping:",{displayTaskId:e.displayTaskId,logicalTaskId:e.logicalTaskId,downloadUrl:eq.downloadUrl}))}},[Z,eq.downloadUrl,nO,t7]);let t8={content_violation:"我们深表歉意，您使用的文本可能违反了Elevenlabs的服务条款，因此已被屏蔽。(此信息由Elevenlabs官方返回)",quota_exceeded:"您的配额已用完，请充值后继续使用。",authentication_failed:"登录会话已过期，请重新登录。",rate_limit_exceeded:"请求过于频繁，请稍后再试。",system_error:"系统暂时繁忙，请稍后再试。",permission_denied:"您没有执行此操作的权限。",invalid_input:"输入参数无效，请检查后重试。"},re=(0,a.useCallback)(e=>void 0!==e.errorType&&void 0!==e.isRetryable?{message:e.message||"",errorType:e.errorType||null,isRetryable:e.isRetryable||!1,isStructured:!0}:{message:e.message||"",errorType:null,isRetryable:!1,isStructured:!1},[]),rn=(0,a.useCallback)((e,n)=>e&&t8[e]?t8[e]:n,[]),rt=(0,a.useCallback)((e,n,t)=>{if(void 0!==n&&void 0!==t)return!0===t;let r=e.toLowerCase();return["too many chunks failed","chunks failed even after retry","system issue","temporary failure","processing timeout","internal server error","service temporarily unavailable","connection reset","request timeout","network error","service unavailable"].some(e=>r.includes(e))},[]),rr=(0,a.useCallback)((e,n)=>{if(n)return["content_violation","quota_exceeded","authentication_failed","permission_denied","invalid_input"].includes(n);let t=e.toLowerCase();return["会员权限不足","配额不足","登录会话已过期","参数验证失败","文本内容违规","非会员用户","token","登录","权限","配额","会员"].some(e=>t.includes(e))},[]),ro=(0,a.useCallback)(()=>{let e=Date.now(),n=Math.random().toString(36).substring(2,11);return"logical_".concat(e,"_").concat(n)},[]),rs=(0,a.useCallback)((e,n)=>{let t=tO.current;t&&tU.current&&(tU.current.updateTaskStatus(t,e,n),console.log("[LOGICAL-TASK] Updated task status:",t,"status:",e))},[]),ra=(0,a.useCallback)(()=>{nG(null),n$.current=null,nV([]);try{sessionStorage.removeItem(nq),sessionStorage.removeItem("current_logical_task_id"),sessionStorage.removeItem("logical_task_timestamp")}catch(e){console.warn("[TASK-MAPPING] Failed to clear sessionStorage:",e)}console.log("[TASK-MAPPING] Cleared task mapping and logical task state")},[]);(0,a.useEffect)(()=>{try{let e=sessionStorage.getItem("current_logical_task_id"),n=sessionStorage.getItem("logical_task_timestamp");if(e&&n){let t=parseInt(n);Date.now()-t<36e5?(nG(e),n$.current=e,console.log("[LOGICAL-TASK] Recovered logical task:",e)):(sessionStorage.removeItem("current_logical_task_id"),sessionStorage.removeItem("logical_task_timestamp"))}}catch(e){console.warn("[LOGICAL-TASK] Failed to recover from sessionStorage:",e)}},[]);let ri=(0,a.useCallback)(function(e){let n,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"",r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:-1,o="https:"===window.location.protocol?"wss:":"ws:";if(r>=0){let e="https://my.aispeak.top".split(",").map(e=>e.trim()).filter(e=>e.length>0);r<e.length?(n=e[r],console.log("[BACKUP-API] Using backup API ".concat(r+1,"/").concat(e.length,":"),n)):(console.warn("[BACKUP-API] Invalid backup index ".concat(r,", max available: ").concat(e.length-1)),n="https://my.aispeak.top")}else n="https://my.aispeak.top";let s=n?new URL(n).host:window.location.host,a="dialogue"===e?"/api/tts/ws/dialogue/generate":"/api/tts/ws/generate",i=null==n?void 0:n.replace("https://","wss://").replace("http://","ws://");return i?"".concat(i).concat(a).concat(t):(console.error("API URL is not configured!"),"".concat(o,"//").concat(s).concat(a).concat(t))},[]),rc=(0,a.useCallback)(()=>"https://my.aispeak.top".split(",").map(e=>e.trim()).filter(e=>e.length>0).length,[]);(0,a.useCallback)(()=>{let e=rc();t3(n=>({...n,maxBackupIndex:e>0?e-1:-1}))},[rc]);let rl=(0,a.useCallback)(e=>{let n={action:"start",token:er.tC.getAccessToken(),model:e.model};return"dialogue"===e.taskType?(n.taskType="dialogue",n.dialogue=e.dialogueLines):(n.input=e.input,n.voice=e.voice),n.stability=e.stability,n.similarity_boost=e.similarity_boost,n.speed=e.speed,"eleven_turbo_v2"!==e.model&&"eleven_turbo_v2_5"!==e.model&&(n.style=e.style),n},[]),rm=(0,a.useCallback)(async e=>{if(n1.isLocked&&"frontend"!==n1.activeRetryType)return;let n=!1,t=0;if(t3(r=>{let o=r.frontendAttempts+1;return o>r.frontendMaxAttempts?{...r,frontendInProgress:!1,isLocked:!1,activeRetryType:null}:(n=!0,t=o,{...r,frontendAttempts:o,frontendInProgress:!0,isLocked:!0,lockReason:"frontend_retry_attempt_".concat(o),activeRetryType:"frontend",frontendStartTime:r.frontendStartTime||Date.now(),errorHistory:[...r.errorHistory,{timestamp:Date.now(),type:"system_temp",message:e,retryType:"frontend"}]})}),!n){J(!1),nF("failed"),nW("多次重试失败"),e9("系统暂时繁忙，请稍后再试或联系客服"),t3(e=>({...e,frontendInProgress:!1,isLocked:!1,activeRetryType:null}));return}try{nW("正在重试... (".concat(t,"/").concat(n1.frontendMaxAttempts,")")),await new Promise(e=>setTimeout(e,3e3));let e=t1();if(!e)throw Error("原始任务数据丢失");let n="dialogue"===e.taskType?"dialogue":"single",r=ri(n),o=new WebSocket(r);rp(o,{retryType:"frontend",attemptNumber:t,maxAttempts:n1.frontendMaxAttempts}),o.onopen=()=>{let n=rl(e);o.send(JSON.stringify(n))}}catch(e){console.error("[FRONTEND-RETRY] Failed to create retry connection:",e),J(!1),nF("failed"),e9("重试连接失败，请稍后再试"),t3(e=>({...e,frontendInProgress:!1,isLocked:!1,activeRetryType:null}))}},[n1,t3,t1,ri,rl]),rd=(0,a.useCallback)(async e=>{let n="https://my.aispeak.top".split(",").map(e=>e.trim()).filter(e=>e.length>0);if(0===n.length){console.log("[BACKUP-API] No valid backup APIs found"),J(!1),nF("failed"),nW("重试失败"),e9("系统暂时繁忙，请稍后再试或联系客服");return}if(n1.isLocked&&"backup"!==n1.activeRetryType)return;let t=!1,r=-1,o=n1.currentBackupIndex+1;if(o>=n.length){console.log("[BACKUP-API] All backup APIs exhausted (".concat(n.length," tried)")),J(!1),nF("failed"),nW("所有备用服务器均不可用"),e9("主备服务器均不可用，请稍后再试或联系客服"),t3(e=>({...e,usingBackupApi:!1,currentBackupIndex:-1,isLocked:!1,activeRetryType:null}));return}if(t3(n=>(t=!0,r=o,{...n,currentBackupIndex:o,usingBackupApi:!0,isLocked:!0,lockReason:"backup_api_".concat(o,"_retry"),activeRetryType:"backup",backupApiStartTime:n.backupApiStartTime||Date.now(),errorHistory:[...n.errorHistory,{timestamp:Date.now(),type:"backup_api_".concat(o),message:e,retryType:"backup"}]})),t)try{let e=n[r];nW("正在尝试备用服务器 ".concat(r+1,"/").concat(n.length,"...")),await new Promise(e=>setTimeout(e,2e3));let t=t1();if(!t)throw Error("原始任务数据丢失");let o="dialogue"===t.taskType?"dialogue":"single",s=ri(o,"",r);console.log("[BACKUP-API] Attempting backup API ".concat(r+1,"/").concat(n.length,": ").concat(e));let a=new WebSocket(s);rp(a,{retryType:"backup",attemptNumber:r+1,maxAttempts:n.length}),a.onopen=()=>{let e=rl(t);a.send(JSON.stringify(e))}}catch(t){console.error("[BACKUP-API-RETRY] Failed to create backup API connection (index ".concat(r,"):"),t),r+1<n.length?(console.log("[BACKUP-API] Trying next backup API..."),setTimeout(()=>{rd(e)},1e3)):(J(!1),nF("failed"),e9("所有备用API连接失败，请稍后再试"),t3(e=>({...e,usingBackupApi:!1,currentBackupIndex:-1,isLocked:!1,activeRetryType:null})))}},[n1,t3,t1,ri,rl]),ru=async(e,n)=>{let t=nY+1;if(t>1){J(!1),nF("failed"),nW("多次重试失败"),e9("检查网络环境，请稍后再试");return}nJ(t),nX(n=>[...n,...e]),nW("正在重试...");try{let r=[...nZ,...e],o=r.length>0?"?excludeLocations=".concat(r.join(",")):"",s=(null==n?void 0:n.taskType)==="dialogue"?"dialogue":"single",a=ri(s,o),i=new WebSocket(a);i.onopen=()=>{i.send(JSON.stringify({action:"retry",recoveryData:n||nQ,retryCount:t,excludedLocations:r}))},rp(i,{retryType:"datacenter",attemptNumber:t,maxAttempts:1})}catch(e){console.error("[RETRY] Failed to create retry connection:",e),J(!1),nF("failed"),e9("重试连接失败，请稍后再试")}},rp=(0,a.useCallback)((e,n)=>{let t=!!n;e.onmessage=r=>{try{let s=JSON.parse(r.data);switch(s.type){case"initialized":if(s.taskId){let e=s.taskId;nU(e),tO.current=e,nV(n=>n.includes(e)?n:[...n,e]);let n=n$.current;if(n){let t=t7();t&&t.logicalTaskId===n?t.physicalTaskIds.includes(e)||(t.physicalTaskIds.push(e),console.log("[TASK-MAPPING] Added physical task to mapping:",t)):(t={logicalTaskId:n,displayTaskId:e,physicalTaskIds:[e],createdAt:Date.now()},tU.current&&tU.current.addTask(t.displayTaskId),console.log("[TASK-MAPPING] New task created:",t)),t9(t)}}nF("processing"),nW("连接成功，任务准备就绪...");break;case"progress":s.message&&nW(s.message);break;case"complete":J(!1),nF("complete"),nW("音频生成完成！"),X(!0),eY({streamUrl:s.streamUrl||null,downloadUrl:s.downloadUrl||null,secureStreamUrl:null}),s.streamUrl&&nl(s.streamUrl).then(e=>{e?(eY(n=>({...n,secureStreamUrl:e})),t_.current&&(t_.current.src=e),console.log("[SECURE-AUDIO] Audio loaded and ready for playback")):e9("音频加载失败，请重试")});let a=s.taskId||tO.current;if(a){let e=t7();e&&e.physicalTaskIds.includes(a)?(tU.current&&tU.current.updateTaskStatus(e.displayTaskId,"complete",s.downloadUrl),console.log("[TASK-MAPPING] Task complete. Updating display task '".concat(e.displayTaskId,"' for physical task '").concat(a,"'."))):(tU.current&&tU.current.updateTaskStatus(a,"complete",s.downloadUrl),console.warn("[TASK-MAPPING] Could not find mapping for completed task '".concat(a,"'. Updating directly.")))}rs("complete",s.downloadUrl),t3(e=>({...e,frontendAttempts:0,datacenterAttempts:0,backupApiAttempts:0,frontendInProgress:!1,usingBackupApi:!1,currentBackupIndex:-1,isLocked:!1,activeRetryType:null,frontendStartTime:0,backupApiStartTime:0,errorHistory:[]})),t2(null),ra(),ra(),t_.current&&s.streamUrl&&(t_.current.src=s.streamUrl),e.close();break;case"error":let i=re(s),c=rn(i.errorType,i.message);if(console.error("[WebSocket] Generation Error:",{original:i.message,errorType:i.errorType,isRetryable:i.isRetryable,isStructured:i.isStructured,userFriendly:c}),t){if("frontend"===n.retryType){if(rt(i.message,i.errorType,i.isRetryable)&&n.attemptNumber<n.maxAttempts){e.close(),setTimeout(()=>{rm(i.message)},1e3);return}{if(!n1.usingBackupApi&&-1===n1.currentBackupIndex){console.log("[RETRY-STRATEGY] Frontend retries exhausted, trying backup API"),e.close(),setTimeout(()=>{rd(i.message)},1e3);return}J(!1),nF("failed"),nW("多次重试失败"),e9("系统暂时繁忙，请稍后再试或联系客服");let n=t7();n&&tU.current&&(tU.current.updateTaskStatus(n.displayTaskId,"failed"),console.log("[TASK-MAPPING] Frontend retry failed, updated display task:",n.displayTaskId)),rs("failed"),t3(e=>({...e,frontendInProgress:!1,usingBackupApi:!1,isLocked:!1,activeRetryType:null})),ra(),e.close();return}}if("backup"===n.retryType){var o;let n=((o="https://my.aispeak.top",void 0===o)?void 0:o.split(",").map(e=>e.trim()).filter(e=>e.length>0))||[],t=n1.currentBackupIndex+1<n.length;if(rt(i.message,i.errorType,i.isRetryable)&&t){console.log("[BACKUP-API] Current backup API failed, trying next one (".concat(n1.currentBackupIndex+2,"/").concat(n.length,")")),e.close(),setTimeout(()=>{rd(i.message)},1e3);return}{J(!1),nF("failed"),nW("所有备用服务器均不可用"),e9("主备服务器均不可用，请稍后再试或联系客服");let n=t7();n&&tU.current&&(tU.current.updateTaskStatus(n.displayTaskId,"failed"),console.log("[TASK-MAPPING] All backup APIs failed, updated display task:",n.displayTaskId)),rs("failed"),t3(e=>({...e,usingBackupApi:!1,currentBackupIndex:-1,isLocked:!1,activeRetryType:null})),ra(),e.close();return}}}rr(i.message,i.errorType)?(J(!1),nF("failed"),nW("生成失败"),tM.current=!1,rs("failed"),console.log("[LOGICAL-TASK] User related error, logical task:",nK||n$.current),t3(e=>({...e,frontendAttempts:0,datacenterAttempts:0,frontendInProgress:!1,isLocked:!1,activeRetryType:null,frontendStartTime:0})),t2(null),ra(),e9(c),"authentication_failed"===i.errorType||i.message.toLowerCase().includes("token")||i.message.toLowerCase().includes("登录")?nn(!0):("quota_exceeded"===i.errorType||i.message.toLowerCase().includes("会员")||i.message.toLowerCase().includes("quota"))&&e8(!0)):rt(i.message,i.errorType,i.isRetryable)&&!n1.frontendInProgress?n1.frontendAttempts<n1.frontendMaxAttempts?(e.close(),setTimeout(()=>{rm(i.message)},1e3)):n1.usingBackupApi||-1!==n1.currentBackupIndex?(J(!1),nF("failed"),nW("系统重试失败"),e9("系统暂时繁忙，请稍后再试或联系客服"),rs("failed"),console.log("[LOGICAL-TASK] System retry failed, logical task:",nK||n$.current)):(console.log("[RETRY-STRATEGY] Frontend retries exhausted, trying backup API for system error"),e.close(),setTimeout(()=>{rd(i.message)},1e3)):(J(!1),nF("failed"),nW("生成失败"),tM.current=!1,rs("failed"),console.log("[LOGICAL-TASK] Other error, logical task:",nK||n$.current),t3(e=>({...e,frontendInProgress:!1,isLocked:!1,activeRetryType:null})),ra(),e9(c||"音频生成失败，请稍后再试！【可尝试切换“梯子”地区】"));break;case"error_retryable":nW("正在重试..."),e.close(),setTimeout(()=>{ru(s.excludeLocations||[],s.taskData)},1e3);break;default:console.warn("[WebSocket] Unknown message type:",s.type)}}catch(e){console.error("[WebSocket] Failed to parse message:",e),e9("收到无法解析的数据")}},e.onclose=()=>{console.log("[WebSocket] Connection closed, isRetryConnection:",t,"retryContext:",n),(!t||n&&n.attemptNumber>=n.maxAttempts||"complete"===nO)&&(Y&&(J(!1),console.log("[WebSocket] Reset isGenerating on close")),setTimeout(()=>{"complete"!==nO&&nW("")},3e3))},e.onerror=e=>{console.error("[WebSocket] Connection error:",e),t||(J(!1),e9("连接失败，请检查网络后重试"))}},[t3,t2,rr,rt,n1.frontendInProgress,rm]),rb=async()=>{if(t_.current){if(Q)t_.current.pause(),ee(!1);else try{if(eq.secureStreamUrl)t_.current.src!==eq.secureStreamUrl&&(t_.current.src=eq.secureStreamUrl);else if(eq.streamUrl){console.log("[PLAYBACK] Loading secure audio for playback...");let e=await nl(eq.streamUrl);if(e)eY(n=>({...n,secureStreamUrl:e})),t_.current.src=e;else throw Error("Failed to load secure audio")}else throw Error("No audio URL available");await t_.current.play(),ee(!0)}catch(e){console.error("Manual playback failed:",e),ee(!1),e9("播放失败，请检查网络连接或重新生成")}}},rg=async()=>{if(eq.downloadUrl)try{let e=new Date,n=e.getFullYear(),t=String(e.getMonth()+1).padStart(2,"0"),r=String(e.getDate()).padStart(2,"0"),o=String(e.getHours()).padStart(2,"0"),s=String(e.getMinutes()).padStart(2,"0"),a=String(e.getSeconds()).padStart(2,"0"),i="tts_".concat(n).concat(t).concat(r,"_").concat(o).concat(s).concat(a,".mp3");await nm(eq.downloadUrl,i)&&console.log("[DOWNLOAD] Download completed successfully with secure authentication")}catch(e){console.error("[DOWNLOAD] Download failed:",e),e9("下载失败，请重试")}},rf=(e,n)=>{if(!t_.current||!Z)return 0;let t=n.getBoundingClientRect(),r=e-t.left,o=t.width,s=Math.max(0,Math.min(r,o)),a=o>0?s/o*100:0;e1(a);let i=t_.current.duration;if(i>0&&!isNaN(i)){let e=Math.max(0,Math.min(a/100*i,i)),n=Math.floor(e/60),t=Math.floor(e%60);eQ("".concat(n.toString().padStart(2,"0"),":").concat(t.toString().padStart(2,"0")))}return a},rx=e=>{if(!t_.current||!Z)return;let n=t_.current.duration;if(n>0&&!isNaN(n)){let t=Math.max(0,Math.min(e/100*n,n));try{t_.current.currentTime=t}catch(e){console.warn("更新音频时间失败:",e)}}},rh=async(e,n)=>{if(e)try{if(nt){nt.pause(),nt.currentTime=0;let e=no!==n;if(nr(null),ns(null),!e)return}let t=new Audio(e);nr(t),ns(n),t.addEventListener("ended",()=>{nr(null),ns(null)}),t.addEventListener("error",()=>{console.error("Preview audio failed to load:",e),nr(null),ns(null)}),await t.play()}catch(e){console.error("Preview audio failed:",e),nr(null),ns(null)}},ry=[{left:15,top:20,duration:10},{left:75,top:35,duration:12},{left:45,top:60,duration:9},{left:85,top:15,duration:11},{left:25,top:80,duration:8},{left:65,top:45,duration:13}];return(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 p-6 relative overflow-hidden",children:[na&&(0,r.jsx)(()=>(0,r.jsx)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:ry.map((e,n)=>(0,r.jsx)("div",{className:"animate-optimized absolute w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float",style:{left:"".concat(e.left,"%"),top:"".concat(e.top,"%"),animationDelay:"".concat(2*n,"s"),animationDuration:"".concat(e.duration,"s")}},n))}),{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" animate-optimized absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-blue-200/50 to-purple-200/40 rounded-full blur-3xl animate-pulse"}),(0,r.jsx)("div",{style:{animationDelay:"2s"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" animate-optimized absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-purple-200/50 to-pink-200/40 rounded-full blur-3xl animate-pulse"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"page-transition-optimized max-w-7xl mx-auto transition-all duration-1000 ".concat(eF?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mb-6",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative z-40 flex items-center justify-between mb-4 p-4 bg-white/80 backdrop-blur-xl rounded-2xl shadow-lg border border-gray-100",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl blur-md opacity-50"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg",children:(0,r.jsx)(x.A,{className:"w-6 h-6 text-white"})})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:[(0,r.jsx)("h1",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gradient-optimized text-xl sm:text-2xl lg:text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent animate-gradient",children:"AI 语音工作室"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" h-0.5 w-20 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mt-1"})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 lg:gap-4",children:[(0,r.jsx)(eh,{ref:tU,getActualTaskIds:t4}),(0,r.jsxs)(i.$,{onClick:()=>window.location.href="/recharge",className:"group relative bg-gradient-to-r from-emerald-500 via-teal-500 to-cyan-500 hover:from-emerald-600 hover:via-teal-600 hover:to-cyan-600 text-white px-3 py-2 lg:px-5 lg:py-3 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 transform hover:scale-110 hover:-translate-y-1 flex items-center gap-1 lg:gap-2 text-sm lg:text-base font-semibold overflow-hidden border border-white/20 backdrop-blur-sm mr-1 lg:mr-4",children:[(0,r.jsx)("div",{style:{animation:"breathe 3s ease-in-out infinite"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-white/10 via-transparent to-white/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,r.jsx)("div",{style:{animation:"breathe-glow 4s ease-in-out infinite"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 rounded-2xl bg-gradient-to-r from-emerald-400 via-teal-400 to-cyan-400 opacity-0 group-hover:opacity-30 blur-sm transition-all duration-500 -z-10"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative w-4 h-4 lg:w-6 lg:h-6 bg-white/20 rounded-full flex items-center justify-center group-hover:bg-white/30 transition-all duration-300 group-hover:rotate-12 group-hover:scale-110",children:[(0,r.jsx)("div",{style:{animation:"breathe 2.5s ease-in-out infinite"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-yellow-300 to-orange-300 rounded-full opacity-60 group-hover:opacity-80 transition-opacity duration-300"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative text-xs lg:text-sm font-bold text-white drop-shadow-sm",children:"\xa5"}),(0,r.jsx)("div",{style:{animation:"breathe 2s ease-in-out infinite"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute -top-1 -right-1 w-2 h-2 bg-yellow-300 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative flex flex-col items-start",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" hidden sm:inline text-white drop-shadow-sm group-hover:text-yellow-100 transition-colors duration-300",children:"充值中心"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" sm:hidden text-white drop-shadow-sm group-hover:text-yellow-100 transition-colors duration-300",children:"充值"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute -bottom-1 left-0 w-0 h-0.5 bg-gradient-to-r from-yellow-300 to-orange-300 group-hover:w-full transition-all duration-500 rounded-full"})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500",children:[void 0,void 0,void 0].map((e,n)=>(0,r.jsx)("div",{style:{left:"".concat(20+25*n,"%"),top:"".concat(30+15*n,"%"),animation:"breathe ".concat(1.5+.3*n,"s ease-in-out infinite"),animationDelay:"".concat(.2*n,"s")},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute w-1 h-1 bg-yellow-300 rounded-full"},n))}),(0,r.jsx)("div",{style:{animation:"breathe 3.5s ease-in-out infinite"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 rounded-2xl bg-white/10 scale-0 group-hover:scale-100 opacity-0 group-hover:opacity-100 transition-all duration-700 ease-out"})]}),(0,r.jsxs)("div",{ref:tR,className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsxs)(i.$,{onClick:()=>eH(!eG),variant:"outline",className:"group relative bg-gradient-to-br from-white/95 via-gray-50/90 to-white/95 backdrop-blur-xl border-2 border-gray-200/60 hover:border-indigo-300/70 hover:bg-gradient-to-br hover:from-indigo-50/80 hover:via-purple-50/70 hover:to-blue-50/80 px-3 py-2 lg:px-4 lg:py-3 rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-500 flex items-center gap-2 lg:gap-3 transform hover:scale-105 hover:-translate-y-0.5 overflow-hidden min-w-[160px] max-w-[200px] lg:min-w-[180px] lg:max-w-[240px]",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-indigo-500/10 via-purple-500/10 to-blue-500/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500 rounded-2xl"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-700 animate-shimmer"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-indigo-400 to-purple-500 rounded-full opacity-0 group-hover:opacity-20 blur-sm transition-all duration-500"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative w-4 h-4 lg:w-6 lg:h-6 bg-gradient-to-br from-indigo-500 via-purple-500 to-blue-600 rounded-full flex items-center justify-center group-hover:rotate-12 transition-all duration-500 shadow-md group-hover:shadow-lg",children:[(0,r.jsx)(h.A,{className:"w-2.5 h-2.5 lg:w-3.5 lg:h-3.5 text-white drop-shadow-sm"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute -top-0.5 -right-0.5 w-2 h-2 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full border border-white shadow-sm",children:(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-green-400 rounded-full animate-ping opacity-75"})})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative hidden md:flex flex-col items-start flex-1 min-w-0",children:[(0,r.jsx)("span",{title:eW,className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm lg:text-base font-semibold bg-gradient-to-r from-gray-700 via-indigo-700 to-purple-700 bg-clip-text text-transparent group-hover:from-indigo-600 group-hover:via-purple-600 group-hover:to-blue-600 transition-all duration-500 leading-tight w-full truncate overflow-hidden text-ellipsis whitespace-nowrap",children:eW}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute -bottom-0.5 left-0 w-0 h-0.5 bg-gradient-to-r from-indigo-400 via-purple-400 to-blue-400 group-hover:w-full transition-all duration-700 rounded-full"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xs text-gray-500 group-hover:text-indigo-600 transition-colors duration-300 font-medium",children:"在线"})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative ml-1",children:(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"w-3 h-3 lg:w-4 lg:h-4 text-gray-400 group-hover:text-indigo-500 transition-all duration-500 ".concat(eG?"rotate-180":""),children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M19 9l-7 7-7-7",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])})})}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 pointer-events-none opacity-0 group-hover:opacity-100 transition-opacity duration-500",children:[void 0,void 0,void 0].map((e,n)=>(0,r.jsx)("div",{style:{left:"".concat(20+25*n,"%"),top:"".concat(30+15*n,"%"),animationDelay:"".concat(.2*n,"s"),animationDuration:"2s"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute w-1 h-1 bg-indigo-400 rounded-full animate-bounce"},n))}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 rounded-2xl bg-gradient-to-r from-indigo-500/20 via-purple-500/20 to-blue-500/20 opacity-0 group-hover:opacity-100 blur-xl transition-all duration-500 -z-10"})]}),eG&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute right-0 top-full mt-2 w-48 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-xl shadow-xl z-50 overflow-hidden",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-2",children:[(0,r.jsxs)("button",{onClick:()=>{n9(!0),eH(!1)},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-full flex items-center gap-3 px-3 py-2 text-left text-gray-700 hover:bg-gray-50 rounded-lg transition-colors duration-200 mb-1",children:[(0,r.jsx)(y.A,{className:"w-4 h-4"}),"修改密码"]}),(0,r.jsxs)("button",{onClick:tX,className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-full flex items-center gap-3 px-3 py-2 text-left text-red-600 hover:bg-red-50 rounded-lg transition-colors duration-200",children:[(0,r.jsx)(w.A,{className:"w-4 h-4"}),"退出登录"]})]})})]})]})]})}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" grid grid-cols-1 lg:grid-cols-3 gap-3 lg:gap-6",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" lg:col-span-2 space-y-3 lg:space-y-6 order-2 lg:order-1",children:[(0,r.jsxs)(c.Zp,{className:"group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 hover:scale-[1.02] relative overflow-hidden",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,r.jsxs)(c.Wu,{className:"pt-4 px-4 pb-2 sm:pt-6 sm:px-6 sm:pb-3 lg:pt-8 lg:px-7 lg:pb-4 relative",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mb-2",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-2 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl",children:(0,r.jsx)(v.A,{className:"w-6 h-6 text-blue-600"})}),(0,r.jsx)("h2",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gradient-optimized text-3xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"文本转语音"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex flex-col items-end",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mb-2 inline-flex items-center gap-2 bg-gradient-to-r from-orange-50 to-red-50 border border-orange-200/50 text-orange-700 text-sm font-medium px-3 py-1.5 rounded-lg shadow-sm animate-pulse",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-2 h-2 bg-orange-500 rounded-full animate-ping"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:'"多人对话"功能限时开放中！'})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative bg-gradient-to-r from-slate-100 via-gray-100 to-slate-100 p-1 rounded-2xl shadow-inner border border-gray-200/50 backdrop-blur-sm",children:[(0,r.jsx)("div",{style:{boxShadow:"single"===tw?"0 4px 20px rgba(59, 130, 246, 0.4), 0 2px 8px rgba(59, 130, 246, 0.2)":"0 4px 20px rgba(147, 51, 234, 0.4), 0 2px 8px rgba(147, 51, 234, 0.2)"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"absolute top-1 bottom-1 w-[calc(50%-2px)] bg-gradient-to-r rounded-xl shadow-lg transition-all duration-500 ease-out transform ".concat("single"===tw?"left-1 from-blue-500 via-blue-600 to-indigo-600":"left-[calc(50%+2px)] from-purple-500 via-purple-600 to-pink-600")}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative flex items-center",children:[(0,r.jsx)("button",{onClick:()=>tY("single"),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"relative z-10 px-6 py-2.5 text-sm font-semibold rounded-xl transition-all duration-500 flex-1 text-center ".concat("single"===tw?"text-white drop-shadow-sm":"text-gray-600 hover:text-gray-800"),children:(0,r.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative z-10 flex items-center justify-center gap-2",children:[(0,r.jsx)(h.A,{className:"w-4 h-4"}),"单人模式"]})}),(0,r.jsxs)("button",{onClick:()=>tY("dialogue"),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"relative z-10 px-6 py-2.5 text-sm font-semibold rounded-xl transition-all duration-500 flex-1 text-center ".concat("dialogue"===tw?"text-white drop-shadow-sm":"text-gray-600 hover:text-gray-800"),children:["single"===tw&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 rounded-xl bg-gradient-to-r from-purple-400/30 via-pink-400/30 to-purple-400/30 animate-pulse-ring-outer pointer-events-none"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/20 via-pink-500/20 to-purple-500/20 animate-pulse-ring-middle pointer-events-none"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 rounded-xl bg-gradient-to-r from-purple-600/10 via-pink-600/10 to-purple-600/10 animate-pulse-ring-inner pointer-events-none"})]}),(0,r.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative z-10 flex items-center justify-center gap-2",children:[(0,r.jsx)(k.A,{className:"w-4 h-4"}),"多人对话"]}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute -top-1 -right-1 z-20 inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-bold bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg border border-white/20 transform scale-90 hover:scale-100 transition-transform duration-200",children:"PRO"})]})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-t from-transparent via-white/10 to-white/20 rounded-2xl pointer-events-none"})]})]})]}),"eleven_v3"===eP&&(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mb-2 flex items-center justify-between",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" inline-flex items-center gap-2 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200/50 text-purple-700 text-sm font-medium px-3 py-2 rounded-lg shadow-sm",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:"✨ v3模型支持情感标注词 目前处于测试阶段"}),(0,r.jsx)("button",{onClick:()=>{let e=[{text:"Okay, you are NOT going to believe this. You know how I've been totally stuck? [frustrated sigh] I was ready to give up. Then last night, just doodling, this random phrase popped into my head. Typed it out and—bam—FLOODGATES. Everything clicked. Character, ending, all of it. I stayed up till 3 AM, typing like a maniac [laughs]. And it's GOOD. It finally feels alive. Like it has a soul. What was a chore now feels like... MAGIC. [happy gasp] I'm still buzzing!",voiceId:"iP95p4xoKVk53GoZ742B"},{text:'In the ancient land of Eldoria, where skies shimmered and forests [whispering] whispered secrets to the wind, lived a dragon named Zephyros. [sarcastic] Not the "burn it all down" kind - [exhales] he was gentle, wise, with eyes like old stars. [softly] Even the birds fell silent when he passed.',voiceId:"cgSgspJ2msm6clMCkdW9"}],n=e[e0];G(n.text),$(n.voiceId),e5((e0+1)%e.length)},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" px-2 py-1 bg-purple-100 hover:bg-purple-200 text-purple-800 text-xs font-semibold rounded transition-colors duration-200 hover:scale-105 transform",children:"查看示例"}),(0,r.jsxs)("button",{onClick:()=>tg(!0),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" px-2 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 hover:from-blue-200 hover:to-indigo-200 text-blue-800 text-xs font-semibold rounded transition-all duration-200 hover:scale-105 transform flex items-center gap-1",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:"\uD83D\uDCA1"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:"了解更多"})]})]}),"dialogue"===tw&&(0,r.jsx)(q,{dialogueLines:tk,voices:l,onImport:tZ,className:"ml-auto"})]})]}),"single"===tw?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[tK,(0,r.jsx)(b,{value:K,onChange:G,onFocus:()=>ev(!0),onBlur:()=>ev(!1),placeholder:"请输入要转换的文本...",className:"min-h-[160px] sm:min-h-[200px] lg:min-h-[270px] max-h-[270px] overflow-y-auto text-base lg:text-lg leading-relaxed resize-none border-2 transition-all duration-500 bg-gradient-to-br from-white to-gray-50/50 p-4 outline-none scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100 ".concat(ew?"border-blue-400 shadow-2xl shadow-blue-100/50 ring-4 ring-blue-50 scale-[1.01]":"border-gray-200 hover:border-gray-300 hover:shadow-lg"),maxLength:tF,onMaxLengthExceeded:t0})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-between mt-2",children:["eleven_v3"===eP?(0,r.jsxs)("button",{onClick:tQ,disabled:tf||!K.trim()||na&&(!et.j2.isLoggedIn()||!nx.isVip||Date.now()>nx.expireAt),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"group relative flex items-center gap-2 px-3 py-1 text-sm font-medium rounded-lg transition-all duration-300 ".concat(tf||!K.trim()||na&&(!et.j2.isLoggedIn()||!nx.isVip||Date.now()>nx.expireAt)?"text-gray-400 bg-gray-100 border border-gray-200 cursor-not-allowed":"text-purple-600 bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200/50 hover:from-purple-100 hover:to-pink-100 hover:border-purple-300 hover:scale-105 hover:shadow-md"),children:[!tf&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-300"}),tf?(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-4 h-4 relative z-10",children:(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-4 h-4 border-2 border-purple-300 border-t-purple-600 rounded-full animate-spin"})}):(0,r.jsx)(j.A,{className:"w-4 h-4 relative z-10 transition-all duration-300 group-hover:scale-110"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative z-10",children:tf?"生成中...":"自动标注"}),na&&(!et.j2.isLoggedIn()||!nx.isVip||Date.now()>nx.expireAt)&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute -top-2 -right-2 w-4 h-4 bg-orange-500 rounded-full flex items-center justify-center",children:(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-white text-xs",children:"!"})})]}):(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 px-3 py-1 text-sm text-gray-500 bg-gray-50 border border-gray-200 rounded-lg",children:[(0,r.jsx)(j.A,{className:"w-4 h-4 text-gray-400"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:"自动标注功能仅在 Eleven v3 模型下可用"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"text-sm px-3 py-1 rounded-lg transition-all duration-300 ".concat(K.length>.8*tF?"bg-orange-100 text-orange-700":"bg-gray-100 text-gray-600"),children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-mono",children:K.length})," / ",tF]})]}),na&&(!et.j2.isLoggedIn()||!nx.isVip||Date.now()>nx.expireAt)&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mt-2 p-3 bg-amber-50 border border-amber-200 rounded-lg",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 text-amber-700",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-2 h-2 bg-amber-500 rounded-full"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm font-medium",children:et.j2.isLoggedIn()?nx.isVip?"会员已过期，请续费后使用":"自动标注功能需要会员权限":"请先登录后使用自动标注功能"})]})})]}):(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3",children:[(0,r.jsx)(en,{dialogueLines:tk,voices:l,voiceIconMapping:p,voiceIcons:W,activeDialogueLineId:tN,onSelectLine:e=>{tz(e),tC(e)},onUpdateText:(e,n)=>tJ(e,"text",n),onRemoveLine:e=>{if(tk.length>1){let n=tk.filter(n=>n.id!==e);tj(n),tN===e&&n.length>0&&tz(n[0].id),tA===e&&(tC(null),e_(!1))}},onTextInputFocus:e=>{tz(e),e_(!1),tC(null)},onEditVoice:e=>{let n=tk.find(n=>n.id===e);n&&$(n.voice),tC(e),tz(e),e_(!0)},containerHeight:350,virtualThreshold:20}),(0,r.jsx)("button",{onClick:()=>{var e;if(tS)return;tT(!0);let n=Math.max(...tk.map(e=>e.id))+1;tj([...tk,{id:n,voice:(null===(e=l[0])||void 0===e?void 0:e.id)||"",text:""}]),tz(n),setTimeout(()=>{let e=document.querySelector('[data-dialogue-line-id="'.concat(n,'"]'));if(e){let n=e.closest(".overflow-y-auto");if(n){let t=n.getBoundingClientRect(),r=e.getBoundingClientRect(),o=n.scrollTop,s=r.top-t.top+o,a=t.height,i=r.height;n.scrollTo({top:Math.max(0,s-a/2+i/2),behavior:"smooth"})}else e.scrollIntoView({behavior:"smooth",block:"center",inline:"nearest"});setTimeout(()=>{let n=e.querySelector('div[contenteditable="true"]');n&&n.focus(),tT(!1)},300)}else tT(!1)},100)},disabled:tS,className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"w-full flex items-center justify-center gap-2 py-3 text-sm font-semibold border-2 border-dashed rounded-2xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 ".concat(tS?"text-gray-400 bg-gray-50 border-gray-200 cursor-not-allowed":"text-blue-600 bg-blue-50 hover:bg-blue-100 border-blue-200 hover:scale-102 hover:shadow-md focus:ring-blue-400"),children:tS?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-4 h-4 border-2 border-gray-300 border-t-gray-600 rounded-full animate-spin"}),"添加中..."]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(N.A,{className:"w-4 h-4"}),"添加对话"]})})]}),(e7||th)&&(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mt-4 space-y-2",children:[e7&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"p-4 rounded-xl border ".concat(e7.includes("违反")||e7.includes("屏蔽")||e7.includes("服务条款")?"bg-orange-50 border-orange-200":e7.includes("配额")||e7.includes("会员")||e7.includes("权限")?"bg-yellow-50 border-yellow-200":"bg-red-50 border-red-200"),children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"flex items-center gap-2 ".concat(e7.includes("违反")||e7.includes("屏蔽")||e7.includes("服务条款")?"text-orange-700":e7.includes("配额")||e7.includes("会员")||e7.includes("权限")?"text-yellow-700":"text-red-700"),children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"w-2 h-2 rounded-full ".concat(e7.includes("违反")||e7.includes("屏蔽")||e7.includes("服务条款")?"bg-orange-500":e7.includes("配额")||e7.includes("会员")||e7.includes("权限")?"bg-yellow-500":"bg-red-500")}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm font-medium",children:e7})]})}),th&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-4 bg-orange-50 border border-orange-200 rounded-xl",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 text-orange-700",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-2 h-2 bg-orange-500 rounded-full"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm font-medium",children:th})]})})]})]})]}),!("eleven_v3"===eP&&!Z)&&(0,r.jsxs)(c.Zp,{className:"group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-10 rounded-2xl overflow-hidden",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,r.jsx)(c.Wu,{className:"p-0 relative",children:Z?(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:[(0,r.jsx)("audio",{ref:t_,src:eq.secureStreamUrl||void 0,onEnded:()=>{ee(!1),e1(0),eQ("00:00"),t_.current&&(t_.current.currentTime=0)},style:{display:"none"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-gradient-to-r from-gray-50 to-blue-50/30 px-6 py-5 pb-8 relative",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-4",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative flex items-center justify-center gap-6",children:[(0,r.jsx)(i.$,{onClick:()=>{t_.current&&(t_.current.currentTime=Math.max(0,t_.current.currentTime-10))},variant:"ghost",size:"sm",className:"w-8 h-8 rounded-full text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-all duration-200 flex-shrink-0 border-0",children:(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-4 h-4",children:(0,r.jsx)("path",{d:"M6 6h2v12H6zm3.5 6l8.5 6V6z",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])})})}),(0,r.jsx)(i.$,{onClick:rb,size:"sm",className:"w-12 h-12 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-200 flex-shrink-0 border-0 hover:scale-105",children:Q?(0,r.jsx)(A.A,{className:"w-5 h-5"}):(0,r.jsx)(C.A,{className:"w-5 h-5 ml-0.5"})}),(0,r.jsx)(i.$,{onClick:()=>{t_.current&&(t_.current.currentTime=Math.min(t_.current.duration||0,t_.current.currentTime+10))},variant:"ghost",size:"sm",className:"w-8 h-8 rounded-full text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-all duration-200 flex-shrink-0 border-0",children:(0,r.jsx)("svg",{fill:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-4 h-4",children:(0,r.jsx)("path",{d:"M16 18h2V6h-2zm-3.5-6L4 6v12z",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])})})}),(0,r.jsx)(i.$,{onClick:rg,variant:"ghost",size:"sm",className:"absolute right-0 w-8 h-8 rounded-full text-gray-600 hover:text-gray-800 hover:bg-gray-100 transition-all duration-200 flex-shrink-0 border-0",children:(0,r.jsx)(S.A,{className:"w-4 h-4"})})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative pb-6",children:[(0,r.jsxs)("div",{onClick:e=>{rx(rf(e.clientX,e.currentTarget))},onMouseDown:e=>{e.preventDefault(),e6(!0);let n=e.currentTarget,t=e=>{e.preventDefault(),requestAnimationFrame(()=>{rf(e.clientX,n)})},r=e=>{e.preventDefault(),rx(rf(e.clientX,n)),e6(!1),document.removeEventListener("mousemove",t),document.removeEventListener("mouseup",r),document.removeEventListener("mouseleave",r)};document.addEventListener("mousemove",t,{passive:!1}),document.addEventListener("mouseup",r,{passive:!1}),document.addEventListener("mouseleave",r,{passive:!1})},title:"拖拽或点击调整播放进度 (".concat(eX," / ").concat(eJ,")"),style:{cursor:e3?"grabbing":"pointer"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"relative bg-gray-200 rounded-full cursor-pointer transition-all duration-200 ".concat(e3?"h-1.5":"h-1 hover:h-1.5"),children:[(0,r.jsx)("div",{style:{width:"".concat(e2,"%"),transition:e3?"none":"width 0.15s ease-out"},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-200"}),e2>0&&(0,r.jsx)("div",{style:{left:"".concat(e2,"%"),marginLeft:"-6px",cursor:e3?"grabbing":"grab",transition:e3?"none":void 0},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"absolute top-1/2 transform -translate-y-1/2 w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full shadow-lg ".concat(e3?"scale-125 opacity-100":"opacity-0 hover:opacity-100 transition-all duration-150")})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute top-6 left-0 text-xs font-mono text-gray-600 font-medium",children:eX}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute top-6 right-0 text-xs font-mono text-gray-600 font-medium",children:eJ})]})]})})]}):(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex flex-col items-center justify-center py-12 text-center px-6",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-14 h-14 bg-gradient-to-r from-blue-100 to-purple-100 rounded-full flex items-center justify-center mb-3",children:(0,r.jsx)(z.A,{className:"w-7 h-7 text-blue-600"})}),(0,r.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg font-semibold text-gray-900 mb-2",children:"您的音频杰作将在此呈现"}),(0,r.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-500 text-sm",children:"选择声音并点击生成，创造完美的语音体验"})]})})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-2 lg:space-y-4 relative z-10 order-1 lg:order-2",children:[(0,r.jsxs)(c.Zp,{className:"group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-30",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,r.jsxs)(c.Wu,{className:"p-6 relative",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-2 bg-gradient-to-r from-green-100 to-blue-100 rounded-xl",children:"dialogue"===tw&&null!==tA?(0,r.jsx)(k.A,{className:"w-5 h-5 text-purple-600"}):(0,r.jsx)(x.A,{className:"w-5 h-5 text-green-600"})}),(0,r.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gradient-optimized text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"dialogue"===tw&&null!==tA?"为对话行选择声音":"选择声音"})]}),(0,r.jsxs)("div",{ref:tE,className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsxs)("button",{onClick:()=>e_(!ek),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"w-full p-3 border-2 rounded-2xl bg-gradient-to-r from-white to-gray-50/50 text-left transition-all duration-500 flex items-center justify-between group/trigger ".concat(ek?"border-blue-400 shadow-2xl shadow-blue-100/50 ring-4 ring-blue-50 scale-[1.02]":"border-gray-200 hover:border-gray-300 hover:shadow-lg"),children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3",children:V?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative w-6 h-6 rounded-full overflow-hidden shadow-lg transition-all duration-300 group-hover/trigger:scale-110",children:(0,r.jsx)("img",{src:p[V]||W[0],alt:null===(e=l.find(e=>e.id===V))||void 0===e?void 0:e.name,onError:e=>{let n=e.target;n.style.display="none";let t=n.parentElement;if(t){var r,o,s;t.innerHTML='\n                                    <div class="w-full h-full rounded-full flex items-center justify-center text-white text-sm font-bold '.concat((null===(r=l.find(e=>e.id===V))||void 0===r?void 0:r.gender)==="male"?"bg-gradient-to-r from-blue-400 to-blue-600":(null===(o=l.find(e=>e.id===V))||void 0===o?void 0:o.gender)==="female"?"bg-gradient-to-r from-pink-400 to-purple-600":"bg-gradient-to-r from-purple-400 to-indigo-600",'">\n                                      ').concat((null===(s=l.find(e=>e.id===V))||void 0===s?void 0:s.name[0])||"?","\n                                    </div>\n                                  ")}},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-full h-full object-cover"})}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-gray-900 text-base",children:null===(n=l.find(e=>e.id===V))||void 0===n?void 0:n.name}),"dialogue"===tw&&null!==tN&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xs text-gray-500",children:null!==tA?"正在编辑: 对话 ".concat(tk.findIndex(e=>e.id===tA)+1):"当前: 对话 ".concat(tk.findIndex(e=>e.id===tN)+1)})]})]}):(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-500 text-base",children:"请选择声音..."})}),(0,r.jsx)(f.A,{className:"w-5 h-5 text-gray-400 transition-all duration-500 group-hover/trigger:text-blue-500 ".concat(ek?"rotate-180 text-blue-500":"")})]}),ek&&(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-2xl shadow-3xl z-50 overflow-hidden transition-all duration-300 opacity-100 translate-y-0 scale-100",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-4 border-b border-gray-100/60 bg-gradient-to-r from-gray-50/30 to-blue-50/20",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative group flex-1",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300"}),(0,r.jsx)(T.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors duration-300 z-10"}),(0,r.jsx)("input",{ref:tL,type:"text",placeholder:"搜索声音名称或描述...",value:ny,onChange:e=>nw(e.target.value),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative w-full pl-11 pr-11 py-3 border-2 border-gray-200/60 rounded-2xl bg-white/90 backdrop-blur-xl text-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500/10 focus:border-blue-400 hover:border-gray-300 hover:shadow-lg transition-all duration-300 shadow-sm"}),ny&&(0,r.jsx)("button",{onClick:()=>nw(""),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute right-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 hover:text-red-500 hover:scale-110 transition-all duration-200 z-10",children:(0,r.jsx)(E.A,{className:"w-4 h-4"})})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:(0,r.jsxs)("button",{onClick:()=>{nE(nj),nL(nz),nR(ny),nP(V),nS(!0)},title:"筛选选项",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"relative p-3 border-2 rounded-2xl transition-all duration-300 flex items-center justify-center group/filter shadow-sm hover:shadow-lg ".concat("all"!==nj||"all"!==nz||ny.trim()?"border-purple-400 bg-gradient-to-r from-purple-50 to-pink-50 text-purple-600 shadow-purple-100/50":"border-gray-200 bg-white hover:border-gray-300 text-gray-500 hover:text-purple-500"),children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 rounded-2xl opacity-0 group-hover/filter:opacity-100 transition-all duration-300"}),(0,r.jsx)(I.A,{className:"w-4 h-4 relative z-10 transition-all duration-300 group-hover/filter:scale-110"}),("all"!==nj||"all"!==nz||ny.trim())&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full border-2 border-white shadow-sm",children:(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-purple-400 rounded-full animate-ping opacity-75"})})]})}),("all"!==nj||"all"!==nz||ny.trim())&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:(0,r.jsxs)("button",{onClick:()=>{nN("all"),nA("all"),nw("")},title:"清除所有筛选条件",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative p-3 border-2 border-red-200 bg-gradient-to-r from-red-50 to-orange-50 text-red-600 rounded-2xl transition-all duration-300 flex items-center justify-center group/clear shadow-sm hover:shadow-lg hover:border-red-300 hover:from-red-100 hover:to-orange-100",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-red-500/5 to-orange-500/5 rounded-2xl opacity-0 group-hover/clear:opacity-100 transition-all duration-300"}),(0,r.jsx)(E.A,{className:"w-4 h-4 relative z-10 transition-all duration-300 group-hover/clear:scale-110"})]})})]})}),("all"!==nj||"all"!==nz||ny.trim())&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" px-4 py-2 bg-gradient-to-r from-blue-50/80 to-purple-50/60 border-t border-gray-100/60 animate-fade-in",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-between text-xs",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 text-gray-600",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:"当前筛选："}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-1",children:["all"!==nj&&(0,r.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium flex items-center gap-1",children:["male"===nj?(0,r.jsx)(ej,{className:"w-3 h-3"}):"female"===nj?(0,r.jsx)(eN,{className:"w-3 h-3"}):(0,r.jsx)(ez,{className:"w-3 h-3"}),"male"===nj?"男生":"female"===nj?"女生":"中性"]}),"all"!==nz&&(0,r.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" px-2 py-1 bg-green-100 text-green-700 rounded-full text-xs font-medium flex items-center gap-1",children:["en"===nz?(0,r.jsx)(eS,{className:"w-3 h-3"}):"ja"===nz?(0,r.jsx)(eT,{className:"w-3 h-3"}):"es"===nz?(0,r.jsx)(eE,{className:"w-3 h-3"}):"ko"===nz?(0,r.jsx)(eI,{className:"w-3 h-3"}):(0,r.jsx)(eL,{className:"w-3 h-3"}),"en"===nz?"英语":"ja"===nz?"日语":"es"===nz?"西班牙语":"ko"===nz?"韩语":"法语"]}),ny.trim()&&(0,r.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" px-2 py-1 bg-purple-100 text-purple-700 rounded-full text-xs font-medium flex items-center gap-1",children:[(0,r.jsx)(T.A,{className:"w-3 h-3"}),'"',ny.length>10?ny.substring(0,10)+"...":ny,'"']})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-blue-600 font-medium",children:[tB.length," 个结果"]})]})}),(0,r.jsx)("div",{ref:tI,className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:(0,r.jsx)(ei,{filteredVoices:tB,onSelectVoice:e=>{$(e),"dialogue"===tw&&null!==tA&&tJ(tA,"voice",e),e_(!1),tC(null)},currentVoiceId:V,previewingVoice:no,handleVoicePreview:rh,voiceIconMapping:p,voiceIcons:W,listHeightClass:"max-h-80"})})]})]})]})]}),(0,r.jsxs)(c.Zp,{className:"group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-20",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-indigo-500/5 to-purple-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,r.jsxs)(c.Wu,{className:"p-6 relative",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-2 bg-gradient-to-r from-indigo-100 to-purple-100 rounded-xl",children:(0,r.jsx)(L.A,{className:"w-5 h-5 text-indigo-600"})}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3",children:[(0,r.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gradient-optimized text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"模型"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative group",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-purple-400/20 to-pink-400/20 rounded-lg blur-sm opacity-0 group-hover:opacity-100 transition-all duration-300"}),(0,r.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative px-3 py-1.5 text-sm font-semibold bg-gradient-to-r from-purple-50 to-pink-50 border border-purple-200/50 rounded-lg text-purple-700 shadow-sm hover:shadow-md transition-all duration-300 hover:scale-105 cursor-default flex items-center gap-1",children:[(0,r.jsx)(j.A,{className:"w-3 h-3"}),"只有Eleven v3才支持情感标注词"]})]})]})]}),(0,r.jsxs)("div",{ref:tD,className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsxs)("button",{onClick:()=>eO(!eU),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"w-full p-3 border-2 rounded-2xl bg-gradient-to-r from-white to-gray-50/50 text-left transition-all duration-500 flex items-center justify-between group/trigger ".concat(eU?"border-indigo-400 shadow-2xl shadow-indigo-100/50 ring-4 ring-indigo-50 scale-[1.02]":"border-gray-200 hover:border-gray-300 hover:shadow-lg"),children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3",children:eP?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg flex items-center justify-center",children:null===(t=tV.find(e=>e.id===eP))||void 0===t?void 0:t.icon}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"font-semibold text-base ".concat("eleven_v3"===eP?"bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent font-bold":"text-gray-900"),children:null===(o=tV.find(e=>e.id===eP))||void 0===o?void 0:o.name})})]}):(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-500 text-base",children:"请选择模型..."})}),(0,r.jsx)(f.A,{className:"w-5 h-5 text-gray-400 transition-all duration-500 group-hover/trigger:text-indigo-500 ".concat(eU?"rotate-180 text-indigo-500":"")})]}),eU&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute top-full left-0 right-0 mt-2 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-2xl shadow-3xl z-50 overflow-hidden transition-all duration-300 opacity-100 translate-y-0 scale-100",children:(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" max-h-85 overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100",children:tV.map(e=>(0,r.jsxs)("div",{onClick:()=>{eM(e.id),eO(!1)},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"relative p-3 border-b border-gray-100 last:border-b-0 cursor-pointer transition-all duration-300 hover:bg-gradient-to-r hover:from-gray-50 hover:to-indigo-50/50 group/item ".concat(eP===e.id?"bg-gradient-to-r from-indigo-50 to-purple-50 border-indigo-100":""),children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-2xl transition-all duration-300 group-hover/item:scale-110 flex items-center justify-center",children:e.icon}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 mb-0.5",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"font-semibold text-base ".concat("eleven_v3"===e.id?"bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent font-bold":"text-gray-900"),children:e.name}),eP===e.id&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-3 h-3 bg-indigo-500 rounded-full animate-ping flex-shrink-0"})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm text-gray-600 leading-relaxed",children:e.description})]})]}),eP===e.id&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute left-0 top-0 bottom-0 w-2 bg-gradient-to-b from-indigo-400 via-purple-500 to-pink-400 rounded-r animate-pulse"})]},e.id))})})]})]})]}),(0,r.jsxs)(c.Zp,{className:"group border-0 shadow-2xl bg-white/90 backdrop-blur-xl hover:shadow-3xl transition-all duration-500 relative z-10 overflow-hidden",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-purple-500/5 to-pink-500/5 opacity-0 group-hover:opacity-100 transition-opacity duration-500"}),(0,r.jsxs)(c.Wu,{className:"p-6 relative",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 mb-4",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-xl",children:(0,r.jsx)(D.A,{className:"w-5 h-5 text-purple-600"})}),(0,r.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gradient-optimized text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:"参数调整"})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-5",children:[{label:"稳定性",value:em,setter:ed,color:"blue",max:1,min:0,step:.01},{label:"相似度",value:eu,setter:ep,color:"purple",max:1,min:0,step:.01},{label:"风格",value:eg,setter:ef,color:"green",max:1,min:0,step:.01},{label:"语速",value:ex,setter:ey,color:"orange",max:1.2,min:.7,step:.01}].filter(e=>"eleven_v3"===eP?"稳定性"===e.label:"eleven_turbo_v2"!==eP&&"eleven_turbo_v2_5"!==eP||"风格"!==e.label).map((e,n)=>(0,r.jsxs)("div",{style:{animationDelay:"".concat(100*n,"ms")},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" group/param",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex justify-between items-center mb-3",children:[(0,r.jsx)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm font-semibold text-gray-700 group-hover/param:text-gray-900 transition-colors duration-300",children:e.label}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"text-sm font-mono px-2.5 py-1.5 rounded-lg font-bold transition-all duration-300 group-hover/param:scale-110 ".concat("blue"===e.color?"bg-blue-100 text-blue-800 group-hover/param:bg-blue-200":"purple"===e.color?"bg-purple-100 text-purple-800 group-hover/param:bg-purple-200":"green"===e.color?"bg-green-100 text-green-800 group-hover/param:bg-green-200":"bg-orange-100 text-orange-800 group-hover/param:bg-orange-200"),children:e.value[0].toFixed(2)})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)(d,{value:e.value,onValueChange:n=>{if("eleven_v3"===eP&&"稳定性"===e.label){let t=n[0];e.setter([t<=.25?0:t<=.75?.5:1])}else e.setter(n)},max:e.max,min:e.min,step:"eleven_v3"===eP&&"稳定性"===e.label?.5:e.step,className:"w-full group-hover/param:scale-105 transition-transform duration-300"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover/param:opacity-100 transition-opacity duration-500 pointer-events-none animate-shimmer"})]})]},e.label))})]})]}),(0,r.jsxs)(i.$,{onClick:()=>{if(tq)return;J(!0),e9(null),nF("processing"),nW("正在建立安全连接..."),X(!1),eY({streamUrl:null,downloadUrl:null,secureStreamUrl:null}),tM.current=!0,ra();let e=ro();nG(e),n$.current=e,console.log("[TASK-MAPPING] Generated new logical task ID:",e),t3(e=>({...e,frontendAttempts:0,datacenterAttempts:0,backupApiAttempts:0,frontendInProgress:!1,usingBackupApi:!1,currentBackupIndex:-1,isLocked:!1,activeRetryType:null,frontendStartTime:0,backupApiStartTime:0,errorHistory:[]})),nJ(0),nX([]),t2({input:K.trim(),voice:V,model:eP,stability:em[0],similarity_boost:eu[0],style:eg[0],speed:ex[0],taskType:"dialogue"===tw?"dialogue":"single",dialogueLines:"dialogue"===tw?tk:void 0});let n=er.tC.getAccessToken();if(!n){nn(!0),J(!1);return}let t=new WebSocket(ri(tw));t.onopen=()=>{nW("连接成功，正在启动任务...");let e={action:"start",token:n,model:eP};"single"===tw?(e.input=K.trim(),e.voice=V):"dialogue"===tw&&(e.taskType="dialogue",e.dialogue=tk.map(e=>({voice:e.voice,text:e.text.trim()}))),"eleven_v3"===eP?e.stability=em[0]:(e.stability=em[0],e.similarity_boost=eu[0],e.speed=ex[0],"eleven_turbo_v2"!==eP&&"eleven_turbo_v2_5"!==eP&&(e.style=eg[0])),t.send(JSON.stringify(e))},rp(t)},disabled:tq,className:"button-hover-optimized w-full text-lg lg:text-xl font-bold relative overflow-hidden group rounded-3xl transition-all duration-500 transform hover:scale-105 disabled:scale-100 shadow-2xl hover:shadow-3xl ".concat(Y?"h-16 sm:h-18 lg:h-20":"h-12 sm:h-14 lg:h-16"," ").concat(tq?"opacity-50 cursor-not-allowed":""),style:{background:"transparent"},children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 rounded-3xl overflow-hidden",children:(0,r.jsx)(ea,{className:"rounded-3xl"})}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-black/20 via-transparent to-black/20 rounded-3xl"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-3xl"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative z-10 text-white font-bold",children:Y||n1.frontendInProgress?(0,r.jsx)(()=>(0,r.jsxs)("div",{className:"flex flex-col items-center gap-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-3",children:[(0,r.jsx)("div",{className:"flex gap-1",children:[0,1,2].map(e=>(0,r.jsx)("div",{className:"w-2 h-2 bg-white rounded-full animate-bounce",style:{animationDelay:"".concat(.15*e,"s")}},e))}),(0,r.jsx)("span",{className:"animate-pulse",children:n1.frontendInProgress?"正在重试... (".concat(n1.frontendAttempts,"/").concat(n1.frontendMaxAttempts,")"):"processing"===nO?"生成中...":"处理中..."})]}),nB&&(0,r.jsx)("div",{className:"text-xs text-white/80 text-center max-w-xs",children:nB})]}),{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])}):(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 text-xl",children:"生成音频"})})]})]})]})]}),(0,r.jsx)(H.lG,{open:e4,onOpenChange:e8,children:(0,r.jsxs)(H.Cf,{className:"sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl",children:[(0,r.jsxs)(H.c7,{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mx-auto w-16 h-16 bg-gradient-to-r from-orange-100 to-red-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(R.A,{className:"w-8 h-8 text-orange-500"})}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-6",children:[(0,r.jsx)(H.L3,{className:"text-2xl font-bold bg-gradient-to-r from-orange-600 to-red-600 bg-clip-text text-transparent",children:"配额不足"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative px-3 py-1.5 rounded-full shadow-sm hover:scale-105 transition-all duration-300 overflow-hidden",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-pink-400 via-red-400 via-orange-400 via-yellow-400 via-green-400 via-teal-400 via-blue-400 via-indigo-400 via-purple-400 to-pink-400 animate-rainbow-bg rounded-full"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative bg-white/90 backdrop-blur-sm rounded-full px-3 py-1.5 m-0.5",children:(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm font-medium bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent",children:"免费体验找客服微信sunshine-12-06"})})]})]}),(0,r.jsxs)(H.rr,{className:"text-gray-600 space-y-3",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-orange-50 rounded-lg border border-orange-200",children:[(0,r.jsx)(R.A,{className:"w-5 h-5 text-orange-500 flex-shrink-0"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm",children:"您当前没有配音权限或配额已用完"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,r.jsx)(v.A,{className:"w-5 h-5 text-blue-500 flex-shrink-0"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm",children:"请充值获取会员权限后继续使用"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,r.jsx)(_.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm",children:"我们提供多种套餐选择，满足不同需求"})]})]})]}),(0,r.jsxs)(H.Es,{className:"flex-col sm:flex-row gap-3 mt-6",children:[(0,r.jsx)(i.$,{variant:"outline",onClick:()=>e8(!1),className:"w-full sm:w-auto border-gray-300 hover:bg-gray-50",children:"稍后再说"}),(0,r.jsxs)(i.$,{onClick:()=>{e8(!1),window.location.href="/recharge"},className:"w-full sm:w-auto bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,r.jsx)(P.A,{className:"w-4 h-4 mr-2"}),"立即充值"]})]})]})}),(0,r.jsx)(H.lG,{open:nd,onOpenChange:nu,children:(0,r.jsxs)(H.Cf,{className:"sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl",children:[(0,r.jsxs)(H.c7,{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mx-auto w-16 h-16 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full flex items-center justify-center",children:(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-8 h-8 text-purple-500",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])})})}),(0,r.jsxs)(H.L3,{className:"text-2xl font-bold",children:["解锁 ",(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent",children:"PRO"})," 功能"]}),(0,r.jsxs)(H.rr,{className:"text-gray-600 space-y-3",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-purple-50 rounded-lg border border-purple-200",children:[(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-5 h-5 text-purple-500 flex-shrink-0",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])})}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm",children:"多人对话功能仅限 PRO 会员使用"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:[(0,r.jsx)(v.A,{className:"w-5 h-5 text-blue-500 flex-shrink-0"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm",children:"升级即可享受更多高级功能"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-green-50 rounded-lg border border-green-200",children:[(0,r.jsx)(_.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm",children:"优先处理队列，更快生成速度"})]})]})]}),(0,r.jsxs)(H.Es,{className:"flex-col sm:flex-row gap-3 mt-6",children:[(0,r.jsx)(i.$,{variant:"outline",onClick:()=>nu(!1),className:"w-full sm:w-auto border-gray-300 hover:bg-gray-50",children:"稍后再说"}),(0,r.jsxs)(i.$,{onClick:()=>{nu(!1),window.location.href="/recharge"},className:"w-full sm:w-auto bg-gradient-to-r from-purple-500 to-pink-600 hover:from-purple-600 hover:to-pink-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,r.jsx)(P.A,{className:"w-4 h-4 mr-2"}),"立即升级"]})]})]})}),(0,r.jsx)(H.lG,{open:ne,onOpenChange:nn,children:(0,r.jsxs)(H.Cf,{className:"sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl",children:[(0,r.jsxs)(H.c7,{className:"text-center space-y-4",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mx-auto w-16 h-16 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center",children:(0,r.jsx)(_.A,{className:"w-8 h-8 text-blue-500"})}),(0,r.jsx)(H.L3,{className:"text-center text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"登录已过期"}),(0,r.jsx)(H.rr,{className:"text-gray-600",children:(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm",children:"您的会话已过期，请重新登录以继续。"})})})]}),(0,r.jsx)(H.Es,{className:"mt-6",children:(0,r.jsxs)(i.$,{onClick:async()=>{nn(!1),await et.j2.logout(),window.location.href="/login"},className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,r.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"重新登录"]})})]})}),(0,r.jsx)(s(),{id:"dc95a5d6ce23cfb7",dynamic:[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""],children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-webkit-keyframes gradient{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@-moz-keyframes gradient{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@-o-keyframes gradient{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@keyframes gradient{0%,100%{background-position:0%50%}50%{background-position:100%50%}}@-webkit-keyframes rainbow-bg{0%{background-position:0%50%;-webkit-filter:hue-rotate(0deg)saturate(1.2)brightness(1.1);filter:hue-rotate(0deg)saturate(1.2)brightness(1.1)}16.67%{background-position:66.67%50%;-webkit-filter:hue-rotate(60deg)saturate(1.3)brightness(1.2);filter:hue-rotate(60deg)saturate(1.3)brightness(1.2)}33.33%{background-position:133.33%50%;-webkit-filter:hue-rotate(120deg)saturate(1.4)brightness(1.1);filter:hue-rotate(120deg)saturate(1.4)brightness(1.1)}50%{background-position:200%50%;-webkit-filter:hue-rotate(180deg)saturate(1.3)brightness(1.2);filter:hue-rotate(180deg)saturate(1.3)brightness(1.2)}66.67%{background-position:266.67%50%;-webkit-filter:hue-rotate(240deg)saturate(1.2)brightness(1.1);filter:hue-rotate(240deg)saturate(1.2)brightness(1.1)}83.33%{background-position:333.33%50%;-webkit-filter:hue-rotate(300deg)saturate(1.3)brightness(1.2);filter:hue-rotate(300deg)saturate(1.3)brightness(1.2)}100%{background-position:400%50%;-webkit-filter:hue-rotate(360deg)saturate(1.2)brightness(1.1);filter:hue-rotate(360deg)saturate(1.2)brightness(1.1)}}@-moz-keyframes rainbow-bg{0%{background-position:0%50%;filter:hue-rotate(0deg)saturate(1.2)brightness(1.1)}16.67%{background-position:66.67%50%;filter:hue-rotate(60deg)saturate(1.3)brightness(1.2)}33.33%{background-position:133.33%50%;filter:hue-rotate(120deg)saturate(1.4)brightness(1.1)}50%{background-position:200%50%;filter:hue-rotate(180deg)saturate(1.3)brightness(1.2)}66.67%{background-position:266.67%50%;filter:hue-rotate(240deg)saturate(1.2)brightness(1.1)}83.33%{background-position:333.33%50%;filter:hue-rotate(300deg)saturate(1.3)brightness(1.2)}100%{background-position:400%50%;filter:hue-rotate(360deg)saturate(1.2)brightness(1.1)}}@-o-keyframes rainbow-bg{0%{background-position:0%50%;filter:hue-rotate(0deg)saturate(1.2)brightness(1.1)}16.67%{background-position:66.67%50%;filter:hue-rotate(60deg)saturate(1.3)brightness(1.2)}33.33%{background-position:133.33%50%;filter:hue-rotate(120deg)saturate(1.4)brightness(1.1)}50%{background-position:200%50%;filter:hue-rotate(180deg)saturate(1.3)brightness(1.2)}66.67%{background-position:266.67%50%;filter:hue-rotate(240deg)saturate(1.2)brightness(1.1)}83.33%{background-position:333.33%50%;filter:hue-rotate(300deg)saturate(1.3)brightness(1.2)}100%{background-position:400%50%;filter:hue-rotate(360deg)saturate(1.2)brightness(1.1)}}@keyframes rainbow-bg{0%{background-position:0%50%;-webkit-filter:hue-rotate(0deg)saturate(1.2)brightness(1.1);filter:hue-rotate(0deg)saturate(1.2)brightness(1.1)}16.67%{background-position:66.67%50%;-webkit-filter:hue-rotate(60deg)saturate(1.3)brightness(1.2);filter:hue-rotate(60deg)saturate(1.3)brightness(1.2)}33.33%{background-position:133.33%50%;-webkit-filter:hue-rotate(120deg)saturate(1.4)brightness(1.1);filter:hue-rotate(120deg)saturate(1.4)brightness(1.1)}50%{background-position:200%50%;-webkit-filter:hue-rotate(180deg)saturate(1.3)brightness(1.2);filter:hue-rotate(180deg)saturate(1.3)brightness(1.2)}66.67%{background-position:266.67%50%;-webkit-filter:hue-rotate(240deg)saturate(1.2)brightness(1.1);filter:hue-rotate(240deg)saturate(1.2)brightness(1.1)}83.33%{background-position:333.33%50%;-webkit-filter:hue-rotate(300deg)saturate(1.3)brightness(1.2);filter:hue-rotate(300deg)saturate(1.3)brightness(1.2)}100%{background-position:400%50%;-webkit-filter:hue-rotate(360deg)saturate(1.2)brightness(1.1);filter:hue-rotate(360deg)saturate(1.2)brightness(1.1)}}@-webkit-keyframes aurora-flow{0%,100%{background-position:0%50%,100%20%,50%80%,0%50%;opacity:.8}25%{background-position:25%30%,75%40%,25%60%,25%30%;opacity:1}50%{background-position:50%70%,50%60%,75%40%,50%70%;opacity:.9}75%{background-position:75%40%,25%80%,50%20%,75%40%;opacity:1}}@-moz-keyframes aurora-flow{0%,100%{background-position:0%50%,100%20%,50%80%,0%50%;opacity:.8}25%{background-position:25%30%,75%40%,25%60%,25%30%;opacity:1}50%{background-position:50%70%,50%60%,75%40%,50%70%;opacity:.9}75%{background-position:75%40%,25%80%,50%20%,75%40%;opacity:1}}@-o-keyframes aurora-flow{0%,100%{background-position:0%50%,100%20%,50%80%,0%50%;opacity:.8}25%{background-position:25%30%,75%40%,25%60%,25%30%;opacity:1}50%{background-position:50%70%,50%60%,75%40%,50%70%;opacity:.9}75%{background-position:75%40%,25%80%,50%20%,75%40%;opacity:1}}@keyframes aurora-flow{0%,100%{background-position:0%50%,100%20%,50%80%,0%50%;opacity:.8}25%{background-position:25%30%,75%40%,25%60%,25%30%;opacity:1}50%{background-position:50%70%,50%60%,75%40%,50%70%;opacity:.9}75%{background-position:75%40%,25%80%,50%20%,75%40%;opacity:1}}@-webkit-keyframes aurora-wave{0%{background-position:-200%0%;opacity:.6}50%{background-position:0%0%;opacity:.8}100%{background-position:200%0%;opacity:.6}}@-moz-keyframes aurora-wave{0%{background-position:-200%0%;opacity:.6}50%{background-position:0%0%;opacity:.8}100%{background-position:200%0%;opacity:.6}}@-o-keyframes aurora-wave{0%{background-position:-200%0%;opacity:.6}50%{background-position:0%0%;opacity:.8}100%{background-position:200%0%;opacity:.6}}@keyframes aurora-wave{0%{background-position:-200%0%;opacity:.6}50%{background-position:0%0%;opacity:.8}100%{background-position:200%0%;opacity:.6}}@-webkit-keyframes aurora-shimmer{0%,100%{background-position:-300%-300%;opacity:.3}50%{background-position:300%300%;opacity:.7}}@-moz-keyframes aurora-shimmer{0%,100%{background-position:-300%-300%;opacity:.3}50%{background-position:300%300%;opacity:.7}}@-o-keyframes aurora-shimmer{0%,100%{background-position:-300%-300%;opacity:.3}50%{background-position:300%300%;opacity:.7}}@keyframes aurora-shimmer{0%,100%{background-position:-300%-300%;opacity:.3}50%{background-position:300%300%;opacity:.7}}@-webkit-keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fade-in{from{opacity:0;-moz-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fade-in{from{opacity:0;-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}@-webkit-keyframes shimmer{0%{-webkit-transform:translatex(-100%);transform:translatex(-100%)}100%{-webkit-transform:translatex(100%);transform:translatex(100%)}}@-moz-keyframes shimmer{0%{-moz-transform:translatex(-100%);transform:translatex(-100%)}100%{-moz-transform:translatex(100%);transform:translatex(100%)}}@-o-keyframes shimmer{0%{-o-transform:translatex(-100%);transform:translatex(-100%)}100%{-o-transform:translatex(100%);transform:translatex(100%)}}@keyframes shimmer{0%{-webkit-transform:translatex(-100%);-moz-transform:translatex(-100%);-o-transform:translatex(-100%);transform:translatex(-100%)}100%{-webkit-transform:translatex(100%);-moz-transform:translatex(100%);-o-transform:translatex(100%);transform:translatex(100%)}}@-webkit-keyframes breathe{0%,100%{opacity:.6;-webkit-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.05);transform:scale(1.05)}}@-moz-keyframes breathe{0%,100%{opacity:.6;-moz-transform:scale(1);transform:scale(1)}50%{opacity:1;-moz-transform:scale(1.05);transform:scale(1.05)}}@-o-keyframes breathe{0%,100%{opacity:.6;-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-o-transform:scale(1.05);transform:scale(1.05)}}@keyframes breathe{0%,100%{opacity:.6;-webkit-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}50%{opacity:1;-webkit-transform:scale(1.05);-moz-transform:scale(1.05);-o-transform:scale(1.05);transform:scale(1.05)}}@-webkit-keyframes breathe-glow{0%,100%{opacity:0;-webkit-transform:scale(.8);transform:scale(.8)}50%{opacity:.4;-webkit-transform:scale(1.2);transform:scale(1.2)}}@-moz-keyframes breathe-glow{0%,100%{opacity:0;-moz-transform:scale(.8);transform:scale(.8)}50%{opacity:.4;-moz-transform:scale(1.2);transform:scale(1.2)}}@-o-keyframes breathe-glow{0%,100%{opacity:0;-o-transform:scale(.8);transform:scale(.8)}50%{opacity:.4;-o-transform:scale(1.2);transform:scale(1.2)}}@keyframes breathe-glow{0%,100%{opacity:0;-webkit-transform:scale(.8);-moz-transform:scale(.8);-o-transform:scale(.8);transform:scale(.8)}50%{opacity:.4;-webkit-transform:scale(1.2);-moz-transform:scale(1.2);-o-transform:scale(1.2);transform:scale(1.2)}}".concat(e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":"",".__jsx-style-dynamic-selector .animate-float.__jsx-style-dynamic-selector{-webkit-animation:float 8s ease-in-out infinite;-moz-animation:float 8s ease-in-out infinite;-o-animation:float 8s ease-in-out infinite;animation:float 8s ease-in-out infinite}.animate-gradient.__jsx-style-dynamic-selector{-webkit-background-size:200%200%;-moz-background-size:200%200%;-o-background-size:200%200%;background-size:200%200%;-webkit-animation:gradient 3s ease infinite;-moz-animation:gradient 3s ease infinite;-o-animation:gradient 3s ease infinite;animation:gradient 3s ease infinite}.animate-fade-in.__jsx-style-dynamic-selector{-webkit-animation:fade-in.5s ease-out forwards;-moz-animation:fade-in.5s ease-out forwards;-o-animation:fade-in.5s ease-out forwards;animation:fade-in.5s ease-out forwards}.animate-shimmer.__jsx-style-dynamic-selector{-webkit-animation:shimmer 2s ease-in-out infinite;-moz-animation:shimmer 2s ease-in-out infinite;-o-animation:shimmer 2s ease-in-out infinite;animation:shimmer 2s ease-in-out infinite}.animate-breathe.__jsx-style-dynamic-selector{-webkit-animation:breathe 3s ease-in-out infinite;-moz-animation:breathe 3s ease-in-out infinite;-o-animation:breathe 3s ease-in-out infinite;animation:breathe 3s ease-in-out infinite}.animate-breathe-glow.__jsx-style-dynamic-selector{-webkit-animation:breathe-glow 4s ease-in-out infinite;-moz-animation:breathe-glow 4s ease-in-out infinite;-o-animation:breathe-glow 4s ease-in-out infinite;animation:breathe-glow 4s ease-in-out infinite}.scrollbar-thin.__jsx-style-dynamic-selector{scrollbar-width:thin}.scrollbar-thumb-gray-300.__jsx-style-dynamic-selector::-webkit-scrollbar-thumb{background-color:#d1d5db;-webkit-border-radius:.375rem;-moz-border-radius:.375rem;border-radius:.375rem}.scrollbar-track-gray-100.__jsx-style-dynamic-selector::-webkit-scrollbar-track{background-color:#f3f4f6}.scrollbar-thin.__jsx-style-dynamic-selector::-webkit-scrollbar{width:6px}@-webkit-keyframes spin-slow{from{-webkit-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);transform:rotate(360deg)}}@-moz-keyframes spin-slow{from{-moz-transform:rotate(0deg);transform:rotate(0deg)}to{-moz-transform:rotate(360deg);transform:rotate(360deg)}}@-o-keyframes spin-slow{from{-o-transform:rotate(0deg);transform:rotate(0deg)}to{-o-transform:rotate(360deg);transform:rotate(360deg)}}@keyframes spin-slow{from{-webkit-transform:rotate(0deg);-moz-transform:rotate(0deg);-o-transform:rotate(0deg);transform:rotate(0deg)}to{-webkit-transform:rotate(360deg);-moz-transform:rotate(360deg);-o-transform:rotate(360deg);transform:rotate(360deg)}}.animate-spin-slow.__jsx-style-dynamic-selector{-webkit-animation:spin-slow 8s linear infinite;-moz-animation:spin-slow 8s linear infinite;-o-animation:spin-slow 8s linear infinite;animation:spin-slow 8s linear infinite}.animate-rainbow-bg.__jsx-style-dynamic-selector{-webkit-background-size:600%100%;-moz-background-size:600%100%;-o-background-size:600%100%;background-size:600%100%;-webkit-animation:rainbow-bg 6s cubic-bezier(.4,0,.2,1)infinite;-moz-animation:rainbow-bg 6s cubic-bezier(.4,0,.2,1)infinite;-o-animation:rainbow-bg 6s cubic-bezier(.4,0,.2,1)infinite;animation:rainbow-bg 6s cubic-bezier(.4,0,.2,1)infinite}")}),(0,r.jsx)(H.lG,{open:n7,onOpenChange:n9,children:(0,r.jsxs)(H.Cf,{className:"sm:max-w-md bg-white/95 backdrop-blur-xl border border-gray-200",children:[(0,r.jsxs)(H.c7,{children:[(0,r.jsxs)(H.L3,{className:"flex items-center gap-2 text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:[(0,r.jsx)(y.A,{className:"w-5 h-5 text-blue-600"}),"修改密码"]}),(0,r.jsx)(H.rr,{className:"text-gray-600",children:"为了您的账户安全，请输入当前密码和新密码"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-4 py-4",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-2",children:[(0,r.jsx)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm font-medium text-gray-700",children:"当前密码"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)(g.p,{type:te?"text":"password",value:n4.currentPassword,onChange:e=>n8(n=>({...n,currentPassword:e.target.value})),placeholder:"请输入当前密码",className:"pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"}),(0,r.jsx)("button",{type:"button",onClick:()=>tn(!te),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:te?(0,r.jsx)(M.A,{className:"w-4 h-4"}):(0,r.jsx)(U.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-2",children:[(0,r.jsx)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm font-medium text-gray-700",children:"新密码"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)(g.p,{type:tt?"text":"password",value:n4.newPassword,onChange:e=>n8(n=>({...n,newPassword:e.target.value})),placeholder:"请输入新密码（至少6位）",className:"pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"}),(0,r.jsx)("button",{type:"button",onClick:()=>tr(!tt),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:tt?(0,r.jsx)(M.A,{className:"w-4 h-4"}):(0,r.jsx)(U.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-2",children:[(0,r.jsx)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm font-medium text-gray-700",children:"确认新密码"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)(g.p,{type:to?"text":"password",value:n4.confirmPassword,onChange:e=>n8(n=>({...n,confirmPassword:e.target.value})),placeholder:"请再次输入新密码",className:"pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"}),(0,r.jsx)("button",{type:"button",onClick:()=>ts(!to),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:to?(0,r.jsx)(M.A,{className:"w-4 h-4"}):(0,r.jsx)(U.A,{className:"w-4 h-4"})})]})]}),ta&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 text-red-700",children:[(0,r.jsx)(R.A,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm font-medium",children:ta})]})})]}),(0,r.jsxs)(H.Es,{className:"flex gap-2",children:[(0,r.jsx)(i.$,{variant:"outline",onClick:()=>{n9(!1),n8({currentPassword:"",newPassword:"",confirmPassword:""}),ti("")},disabled:tc,className:"flex-1",children:"取消"}),(0,r.jsx)(i.$,{onClick:t5,disabled:tc,className:"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white",children:tc?(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),"修改中..."]}):"确认修改"})]})]})}),(0,r.jsx)(H.lG,{open:tm,onOpenChange:td,children:(0,r.jsxs)(H.Cf,{className:"sm:max-w-md bg-white/95 backdrop-blur-xl border border-gray-200",children:[(0,r.jsxs)(H.c7,{children:[(0,r.jsxs)(H.L3,{className:"flex items-center gap-3 text-xl font-bold bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-md opacity-50"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative p-2 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full shadow-lg",children:(0,r.jsx)(O.A,{className:"w-6 h-6 text-white"})})]}),"密码修改成功"]}),(0,r.jsx)(H.rr,{className:"text-gray-600 text-center py-4 text-lg",children:"您的密码已成功更新，请妥善保管新密码"})]}),(0,r.jsx)(H.Es,{className:"flex justify-center",children:(0,r.jsx)(i.$,{onClick:()=>td(!1),className:"px-8 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105",children:"确定"})})]})}),(0,r.jsx)(H.lG,{open:tu,onOpenChange:tp,children:(0,r.jsxs)(H.Cf,{className:"sm:max-w-4xl max-h-[95vh] bg-white/95 backdrop-blur-xl border-0 shadow-2xl overflow-y-auto",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-br from-orange-50/80 via-red-50/60 to-yellow-50/80"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-orange-200/30 to-red-200/30 rounded-full blur-3xl"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-yellow-200/30 to-orange-200/30 rounded-full blur-2xl"}),(0,r.jsx)("button",{onClick:()=>tp(!1),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute top-4 right-4 z-20 group p-2 rounded-full bg-white/80 hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-red-400 to-orange-400 rounded-full blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-300"}),(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative w-5 h-5 text-gray-600 group-hover:text-red-500 transition-colors duration-300",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M6 18L18 6M6 6l12 12",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])})})]})}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative z-10",children:[(0,r.jsxs)(H.c7,{className:"text-center space-y-4 pb-2",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-center gap-3 mb-2",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-orange-400 to-red-400 rounded-full blur-lg opacity-50 animate-pulse"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative p-3 bg-gradient-to-r from-orange-500 via-red-500 to-yellow-500 rounded-full shadow-xl",children:(0,r.jsx)(R.A,{className:"w-8 h-8 text-white animate-bounce",style:{animationDuration:"2s"}})})]})}),(0,r.jsx)(H.L3,{className:"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-orange-600 via-red-600 to-yellow-600 bg-clip-text text-transparent leading-tight text-center",children:"\uD83D\uDCE2 重要公告"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-2xl font-semibold text-center text-gray-800",children:"关于 ElevenLabs 内容审核机制"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-center gap-2 py-2",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent flex-1"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-2 h-2 bg-gradient-to-r from-orange-400 to-red-400 rounded-full animate-pulse"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" h-px bg-gradient-to-r from-transparent via-orange-300 to-transparent flex-1"})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3 py-1",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-center",children:(0,r.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xl text-gray-700 font-bold leading-relaxed font-medium",children:"尊敬的用户，您好！"})}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-gradient-to-r from-red-50/90 to-orange-50/90 rounded-3xl p-8 border-2 border-red-200/60 shadow-xl",children:[(0,r.jsxs)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-2xl font-bold text-gray-800 mb-6 flex items-center justify-center gap-3",children:[(0,r.jsx)(R.A,{className:"w-7 h-7 text-red-600"}),"内容政策提醒"]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-center mb-6",children:(0,r.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg text-gray-700 leading-relaxed",children:["请注意 ElevenLabs 官方会对所有提交的文本内容进行",(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-red-600",children:"自动审核"}),"。"]})}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-white/80 backdrop-blur-sm rounded-2xl p-6 border border-red-200/60 shadow-lg",children:[(0,r.jsx)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xl font-bold text-gray-800 mb-4 flex items-center justify-center gap-2",children:"⚠️ 可能被拦截的内容"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" grid grid-cols-1 md:grid-cols-2 gap-4 text-left",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700",children:"暴力或威胁性内容"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700",children:"仇恨言论或歧视性内容"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700",children:"成人或性暗示内容"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700",children:"政治敏感内容"})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700",children:"版权保护的内容（如歌词、台词等）"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700",children:"可能用于欺诈的内容"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 p-3 bg-red-50 rounded-lg border border-red-200",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-red-600 font-bold",children:"\uD83D\uDEAB"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700",children:"其他违反 ElevenLabs 服务条款的内容"})]})]})]})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-gradient-to-r from-blue-50/90 to-indigo-50/90 rounded-2xl p-6 border border-blue-200/60 shadow-lg max-w-4xl mx-auto",children:[(0,r.jsxs)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xl font-bold text-gray-800 mb-4 flex items-center justify-center gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-2xl",children:"\uD83D\uDCA1"}),"温馨提示"]}),(0,r.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700 leading-relaxed text-center max-w-3xl mx-auto text-lg",children:["为确保您的配音请求能够顺利处理，请在提交前检查文本内容是否符合 ElevenLabs 的",(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-blue-700",children:"内容政策"}),"。如果您的内容被误判，可以尝试调整表达方式。"]})]}),(0,r.jsxs)("details",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" group",children:[(0,r.jsx)("summary",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" cursor-pointer list-none",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-gray-200/60 shadow-lg hover:shadow-xl transition-all duration-300 group-open:rounded-b-none",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-between",children:[(0,r.jsxs)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xl font-bold text-gray-800 flex items-center gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xl",children:"\uD83D\uDCCB"}),"审核机制详细说明"]}),(0,r.jsx)(f.A,{className:"w-5 h-5 text-gray-500 group-open:rotate-180 transition-transform duration-300"})]}),(0,r.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-600 mt-2",children:"点击查看详细说明"})]})}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-white/60 backdrop-blur-sm rounded-b-2xl p-6 border border-t-0 border-gray-200/60 shadow-lg",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-6 max-w-4xl mx-auto",children:[(0,r.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700 leading-relaxed text-lg",children:["ElevenLabs 作为全球领先的AI语音合成平台，严格遵循",(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-blue-600",children:"国际内容安全标准"}),"和",(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-green-600",children:"法律法规要求"}),"，对所有用户提交的文本内容实施自动化审核机制。"]}),(0,r.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700 leading-relaxed text-lg",children:["该审核系统采用先进的AI技术，能够识别和拦截可能违反服务条款的内容。这一机制旨在",(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-purple-600",children:"保护用户权益"}),"、",(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-orange-600",children:"维护平台安全"}),"，并确保AI语音技术的负责任使用。"]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-gradient-to-r from-yellow-50 to-orange-50 rounded-xl p-6 border border-yellow-200/50",children:[(0,r.jsxs)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg font-bold text-gray-800 mb-3 flex items-center gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xl",children:"⚡"}),"如何避免内容被拦截？"]}),(0,r.jsxs)("ul",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-2 text-gray-700",children:[(0,r.jsxs)("li",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-start gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-green-600 font-bold mt-1",children:"✓"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:"使用积极正面的表达方式"})]}),(0,r.jsxs)("li",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-start gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-green-600 font-bold mt-1",children:"✓"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:"避免使用敏感词汇和争议性话题"})]}),(0,r.jsxs)("li",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-start gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-green-600 font-bold mt-1",children:"✓"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:"确保内容符合当地法律法规"})]}),(0,r.jsxs)("li",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-start gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-green-600 font-bold mt-1",children:"✓"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:"使用原创内容，避免版权争议"})]})]})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-200/50",children:(0,r.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-700 leading-relaxed text-lg text-center",children:"我们理解内容审核可能会给您的创作带来一定限制，但这是为了确保平台的长期稳定运行和所有用户的安全体验。感谢您的理解与配合，让我们共同维护一个健康、安全的AI语音创作环境！"})})]})})]})]}),(0,r.jsx)(H.Es,{className:"flex justify-center pt-4",children:(0,r.jsxs)(i.$,{onClick:()=>tp(!1),className:"px-8 py-3 bg-gradient-to-r from-orange-500 via-red-500 to-yellow-500 hover:from-orange-600 hover:via-red-600 hover:to-yellow-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2",children:[(0,r.jsx)(O.A,{className:"w-5 h-5"}),"我知道了"]})})]})]})}),(0,r.jsx)(H.lG,{open:tb,onOpenChange:tg,children:(0,r.jsxs)(H.Cf,{className:"sm:max-w-2xl bg-white/95 backdrop-blur-xl border-0 shadow-2xl overflow-hidden",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-br from-purple-50/80 via-pink-50/60 to-blue-50/80"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-200/30 to-pink-200/30 rounded-full blur-3xl"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute bottom-0 left-0 w-24 h-24 bg-gradient-to-tr from-blue-200/30 to-indigo-200/30 rounded-full blur-2xl"}),(0,r.jsx)("button",{onClick:()=>tg(!1),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute top-4 right-4 z-20 group p-2 rounded-full bg-white/80 hover:bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-md opacity-0 group-hover:opacity-30 transition-opacity duration-300"}),(0,r.jsx)("svg",{fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative w-5 h-5 text-gray-600 group-hover:text-purple-500 transition-colors duration-300",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2.5,d:"M6 18L18 6M6 6l12 12",className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])})})]})}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative z-10",children:[(0,r.jsxs)(H.c7,{className:"text-center space-y-4 pb-2",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-center gap-3 mb-2",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full blur-lg opacity-50 animate-pulse"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative p-3 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 rounded-full shadow-xl",children:(0,r.jsx)(j.A,{className:"w-8 h-8 text-white animate-spin",style:{animationDuration:"3s"}})})]})}),(0,r.jsx)(H.L3,{className:"text-2xl sm:text-3xl font-bold bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 bg-clip-text text-transparent leading-tight text-center",children:"✨ 释放创造力：全新 v3 模型登场！"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-center gap-2 py-2",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" h-px bg-gradient-to-r from-transparent via-purple-300 to-transparent flex-1"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-2 h-2 bg-gradient-to-r from-purple-400 to-pink-400 rounded-full animate-pulse"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" h-px bg-gradient-to-r from-transparent via-purple-300 to-transparent flex-1"})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-6 py-4",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-center",children:(0,r.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg text-gray-700 leading-relaxed font-medium",children:["全新的 v3 模型不再是冰冷地朗读文字。它能理解您植入的",(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-purple-600 font-semibold",children:'"情感标签"'}),"，像一位专业配音演员，用最恰当的语调和情感来演绎您的文本。"]})}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-white/60 backdrop-blur-sm rounded-2xl p-6 border border-purple-200/50 shadow-lg",children:[(0,r.jsxs)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xl font-bold text-gray-800 mb-4 flex items-center gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-2xl",children:"\uD83C\uDFAD"}),"您可以尝试在文本中使用以下标签，探索各种可能性："]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" grid grid-cols-1 sm:grid-cols-3 gap-4",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3",children:[(0,r.jsxs)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-purple-700 flex items-center gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg",children:"\uD83D\uDE0A"}),"情绪类："]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex flex-wrap gap-2",children:["[愉快]","[悲伤]","[愤怒]","[激动]","[担忧]","[惊讶]"].map((e,n)=>(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" px-2 py-1 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 text-sm font-medium rounded-lg border border-purple-200/50 hover:scale-105 transition-transform duration-200 cursor-default",children:e},n))})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3",children:[(0,r.jsxs)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-blue-700 flex items-center gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg",children:"\uD83C\uDFA8"}),"风格类："]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex flex-wrap gap-2",children:["[旁白]","[耳语]","[轻声]","[严肃]","[广告语气]"].map((e,n)=>(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" px-2 py-1 bg-gradient-to-r from-blue-100 to-indigo-100 text-blue-700 text-sm font-medium rounded-lg border border-blue-200/50 hover:scale-105 transition-transform duration-200 cursor-default",children:e},n))})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3",children:[(0,r.jsxs)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-green-700 flex items-center gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg",children:"\uD83C\uDFAC"}),"动作类："]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex flex-wrap gap-2",children:["[笑声]","[叹气]"].map((e,n)=>(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" px-2 py-1 bg-gradient-to-r from-green-100 to-emerald-100 text-green-700 text-sm font-medium rounded-lg border border-green-200/50 hover:scale-105 transition-transform duration-200 cursor-default",children:e},n))})]})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mt-6 p-4 bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200/50 rounded-xl",children:(0,r.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-center text-gray-700 font-medium flex items-center justify-center gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xl",children:"\uD83D\uDCA1"}),(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]]),children:"提示：您可以自由组合，创造出独一无二的声音效果！"})]})})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-center",children:(0,r.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm text-gray-500 flex items-center justify-center gap-2",children:[(0,r.jsx)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-2 h-2 bg-blue-400 rounded-full animate-pulse"}),"此弹窗将在 5 秒后自动关闭"]})})]}),(0,r.jsx)(H.Es,{className:"flex justify-center pt-4",children:(0,r.jsxs)(i.$,{onClick:()=>tg(!1),className:"px-8 py-3 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 hover:from-purple-600 hover:via-pink-600 hover:to-blue-600 text-white font-semibold rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 flex items-center gap-2",children:[(0,r.jsx)(O.A,{className:"w-5 h-5"}),"我知道了"]})})]})]})}),(0,r.jsx)(H.lG,{open:nC,onOpenChange:nS,children:(0,r.jsx)(H.Cf,{className:"max-w-6xl max-h-[90vh] overflow-hidden p-0 bg-white/95 backdrop-blur-xl border border-gray-200 rounded-3xl shadow-3xl",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex h-[80vh]",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-80 bg-gradient-to-br from-purple-50/80 to-pink-50/60 border-r border-gray-200/60 p-6 overflow-y-auto",children:[(0,r.jsxs)(H.c7,{className:"mb-6",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-2 bg-gradient-to-r from-purple-100 to-pink-100 rounded-xl",children:(0,r.jsx)(I.A,{className:"w-6 h-6 text-purple-600"})}),(0,r.jsx)(H.L3,{className:"text-2xl font-bold text-gray-900",children:"筛选声音"})]}),(0,r.jsx)(H.rr,{className:"text-gray-600 mt-2",children:"设置筛选条件，找到最适合的声音"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-6",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3",children:[(0,r.jsxs)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 text-sm font-semibold text-gray-700",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-1 bg-gradient-to-r from-blue-100 to-purple-100 rounded",children:(0,r.jsx)(T.A,{className:"w-3 h-3 text-blue-600"})}),"搜索声音"]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative group",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5 rounded-2xl opacity-0 group-hover:opacity-100 transition-all duration-300"}),(0,r.jsx)(T.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 group-hover:text-blue-500 transition-colors duration-300 z-10"}),(0,r.jsx)("input",{type:"text",placeholder:"搜索声音名称或描述...",value:nD,onChange:e=>nR(e.target.value),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" relative w-full pl-11 pr-4 py-3 border-2 border-gray-200/60 rounded-2xl bg-white/90 backdrop-blur-xl text-sm placeholder-gray-400 focus:outline-none focus:ring-4 focus:ring-blue-500/10 focus:border-blue-400 hover:border-gray-300 hover:shadow-lg transition-all duration-300 shadow-sm"}),nD&&(0,r.jsx)("button",{onClick:()=>nR(""),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute right-3 top-1/2 transform -translate-y-1/2 p-1 text-gray-400 hover:text-gray-600 transition-colors duration-200",children:(0,r.jsx)(E.A,{className:"w-4 h-4"})})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3",children:[(0,r.jsxs)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 text-sm font-semibold text-gray-700",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-1 bg-gradient-to-r from-green-100 to-blue-100 rounded",children:(0,r.jsx)(k.A,{className:"w-3 h-3 text-green-600"})}),"性别筛选"]}),(0,r.jsx)(eR,{value:nT,onChange:e=>nE(e),options:[{value:"all",label:"全部声音",icon:(0,r.jsx)(k.A,{className:"w-4 h-4"})},{value:"male",label:"男生声音",icon:(0,r.jsx)(eA,{className:"w-4 h-4"})},{value:"female",label:"女生声音",icon:(0,r.jsx)(eC,{className:"w-4 h-4"})}],className:"border-green-400 focus:ring-green-500/10",hoverColor:"green"})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3",children:[(0,r.jsxs)("label",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 text-sm font-semibold text-gray-700",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-1 bg-gradient-to-r from-orange-100 to-red-100 rounded",children:(0,r.jsx)(F.A,{className:"w-3 h-3 text-orange-600"})}),"语言筛选"]}),(0,r.jsx)(eR,{value:nI,onChange:e=>nL(e),options:[{value:"all",label:"全部语言",icon:(0,r.jsx)(eD,{className:"w-4 h-4"})},{value:"en",label:"英语",icon:(0,r.jsx)(eS,{className:"w-4 h-4"})},{value:"ja",label:"日语",icon:(0,r.jsx)(eT,{className:"w-4 h-4"})},{value:"es",label:"西班牙语",icon:(0,r.jsx)(eE,{className:"w-4 h-4"})},{value:"ko",label:"韩语",icon:(0,r.jsx)(eI,{className:"w-4 h-4"})},{value:"fr",label:"法语",icon:(0,r.jsx)(eL,{className:"w-4 h-4"})}],className:"border-orange-400 focus:ring-orange-500/10",hoverColor:"orange"})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" p-4 bg-gradient-to-r from-blue-50/80 to-purple-50/60 rounded-2xl border border-blue-200/50",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-between",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm text-gray-600 font-medium",children:"筛选结果"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg font-bold text-blue-600",children:[tW.length," 个声音"]})]})}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" space-y-3 pt-4 border-t border-gray-200/60",children:[(0,r.jsx)("button",{onClick:()=>{nE("all"),nL("all"),nR("")},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-full px-4 py-3 text-sm text-red-600 hover:text-gray-800 bg-gray-50 hover:bg-gray-100 rounded-2xl transition-all duration-200 font-medium",children:"重置筛选"}),(0,r.jsxs)("button",{onClick:tG,className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-full px-4 py-3 text-sm text-white bg-gradient-to-r from-purple-500 to-pink-500 hover:from-purple-600 hover:to-pink-600 rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 font-semibold flex items-center justify-center gap-2",children:[(0,r.jsx)(O.A,{className:"w-4 h-4"}),"应用筛选 (",tW.length,")"]})]})]})]}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex-1 p-6 overflow-y-auto",children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" mb-4",children:[(0,r.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg font-semibold text-gray-900 mb-2",children:"筛选结果"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center justify-between",children:[(0,r.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm text-gray-600",children:["共找到 ",tW.length," 个符合条件的声音"]}),(0,r.jsxs)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-xs text-violet-500 bg-gray-50 px-2 py-1 rounded-lg flex items-center gap-1",children:[(0,r.jsx)(j.A,{className:"w-3 h-3"}),"双击声音卡片快速选择并应用"]})]})]}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:tW.map(e=>(0,r.jsxs)("div",{onClick:()=>{nP(e.id)},onDoubleClick:()=>{nP(e.id),tG()},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"group relative p-4 border-2 rounded-2xl transition-all duration-300 cursor-pointer hover:shadow-lg ".concat(n_===e.id?"border-blue-400 bg-gradient-to-r from-blue-50 to-purple-50 shadow-lg":"border-gray-200 bg-white hover:border-gray-300"),children:[(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-3 mb-3",children:[(0,r.jsx)("img",{src:p[e.id],alt:e.name,className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" w-10 h-10 rounded-full object-cover shadow-md"}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" font-semibold text-gray-900 truncate",children:e.name}),(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-center gap-2 text-xs",children:[(0,r.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" "+"px-2 py-1 rounded-full text-white font-medium flex items-center gap-1 ".concat("male"===e.gender?"bg-blue-500":"female"===e.gender?"bg-pink-500":"bg-gray-500"),children:["male"===e.gender?(0,r.jsx)(ej,{className:"w-3 h-3"}):"female"===e.gender?(0,r.jsx)(eN,{className:"w-3 h-3"}):(0,r.jsx)(ez,{className:"w-3 h-3"}),"male"===e.gender?"男":"female"===e.gender?"女":"中性"]}),(0,r.jsxs)("span",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" px-2 py-1 bg-gray-100 text-gray-700 rounded-full font-medium flex items-center gap-1",children:["en"===e.language?(0,r.jsx)(eS,{className:"w-3 h-3"}):"ja"===e.language?(0,r.jsx)(eT,{className:"w-3 h-3"}):"es"===e.language?(0,r.jsx)(eE,{className:"w-3 h-3"}):"ko"===e.language?(0,r.jsx)(eI,{className:"w-3 h-3"}):"fr"===e.language?(0,r.jsx)(eL,{className:"w-3 h-3"}):(0,r.jsx)(eD,{className:"w-3 h-3"}),"en"===e.language?"英语":"ja"===e.language?"日语":"es"===e.language?"西班牙语":"ko"===e.language?"韩语":"fr"===e.language?"法语":"其他"]})]})]})]}),(0,r.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm text-gray-600 line-clamp-2",children:e.description}),e.preview&&(0,r.jsx)("button",{onClick:n=>{n.stopPropagation(),rh(e.preview,e.id)},className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute top-3 right-3 p-2 bg-white/80 hover:bg-white rounded-full shadow-md hover:shadow-lg transition-all duration-200 opacity-0 group-hover:opacity-100",children:no===e.id?(0,r.jsx)(A.A,{className:"w-4 h-4 text-blue-600"}):(0,r.jsx)(C.A,{className:"w-4 h-4 text-blue-600"})}),n_===e.id&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute top-3 left-3 w-3 h-3 bg-blue-500 rounded-full border-2 border-white shadow-md",children:(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" absolute inset-0 bg-blue-400 rounded-full animate-ping opacity-75"})})]},e.id))}),0===tW.length&&(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-center py-12",children:[(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-400 mb-4",children:(0,r.jsx)(T.A,{className:"w-16 h-16 mx-auto"})}),(0,r.jsx)("h3",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-lg font-semibold text-gray-600 mb-2",children:"没有找到匹配的声音"}),(0,r.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-gray-500",children:"请尝试调整筛选条件"})]})]})]})})}),np&&(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm",children:(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" bg-orange-500 text-white px-6 py-4 rounded-lg shadow-xl max-w-md mx-4 animate-in zoom-in-95 duration-300",children:(0,r.jsxs)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex items-start gap-3",children:[(0,r.jsx)(R.A,{className:"w-5 h-5 mt-0.5 flex-shrink-0"}),(0,r.jsx)("div",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" flex-1",children:(0,r.jsx)("p",{className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-sm font-medium",children:ng})}),(0,r.jsx)("button",{onClick:()=>nb(!1),className:s().dynamic([["dc95a5d6ce23cfb7",[e3?"\n          * {\n            user-select: none !important;\n            -webkit-user-select: none !important;\n            -moz-user-select: none !important;\n            -ms-user-select: none !important;\n          }\n          body {\n            cursor: grabbing !important;\n          }\n        ":""]]])+" text-white/80 hover:text-white transition-colors",children:(0,r.jsx)(E.A,{className:"w-4 h-4"})})]})})})]})}},7492:(e,n,t)=>{"use strict";t.d(n,{A:()=>a,tu:()=>s});let r={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"};function o(e){if(null==e?void 0:e.code)return Object.values(r).includes(e.code);if(null==e?void 0:e.message){let n=e.message.toLowerCase();return n.includes("token")||n.includes("expired")||n.includes("unauthorized")||n.includes("401")||n.includes("登录")||n.includes("refresh")}return!1}function s(e,n){let t=o(e);t&&n&&n();let r=o(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:(null==e?void 0:e.shouldRedirect)===void 0||e.shouldRedirect}:{title:"操作失败",description:(null==e?void 0:e.message)||"请检查网络连接后重试",shouldRedirect:!1};return{isAuthError:t,message:r.description,shouldRedirect:r.shouldRedirect}}function a(e){var n;return o(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:(null==e?void 0:null===(n=e.message)||void 0===n?void 0:n.includes("卡密"))?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:(null==e?void 0:e.message)||"请检查网络连接后重试",variant:"destructive"}}},8827:(e,n,t)=>{Promise.resolve().then(t.bind(t,7053))},9840:(e,n,t)=>{"use strict";t.d(n,{Cf:()=>d,Es:()=>p,L3:()=>b,c7:()=>u,lG:()=>c,rr:()=>g});var r=t(5155),o=t(2115),s=t(3651),a=t(4416),i=t(3999);let c=s.bL;s.l9;let l=s.ZL;s.bm;let m=o.forwardRef((e,n)=>{let{className:t,...o}=e;return(0,r.jsx)(s.hJ,{ref:n,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...o})});m.displayName=s.hJ.displayName;let d=o.forwardRef((e,n)=>{let{className:t,children:o,...c}=e;return(0,r.jsxs)(l,{children:[(0,r.jsx)(m,{}),(0,r.jsxs)(s.UC,{ref:n,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...c,children:[o,(0,r.jsxs)(s.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(a.A,{className:"h-4 w-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});d.displayName=s.UC.displayName;let u=e=>{let{className:n,...t}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",n),...t})};u.displayName="DialogHeader";let p=e=>{let{className:n,...t}=e;return(0,r.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",n),...t})};p.displayName="DialogFooter";let b=o.forwardRef((e,n)=>{let{className:t,...o}=e;return(0,r.jsx)(s.hE,{ref:n,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",t),...o})});b.displayName=s.hE.displayName;let g=o.forwardRef((e,n)=>{let{className:t,...o}=e;return(0,r.jsx)(s.VY,{ref:n,className:(0,i.cn)("text-sm text-muted-foreground",t),...o})});g.displayName=s.VY.displayName},9852:(e,n,t)=>{"use strict";t.d(n,{p:()=>a});var r=t(5155),o=t(2115),s=t(3999);let a=o.forwardRef((e,n)=>{let{className:t,type:o,...a}=e;return(0,r.jsx)("input",{type:o,className:(0,s.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),ref:n,...a})});a.displayName="Input"}},e=>{var n=n=>e(e.s=n);e.O(0,[352,150,651,550,678,576,441,684,358],()=>n(8827)),_N_E=e.O()}]);