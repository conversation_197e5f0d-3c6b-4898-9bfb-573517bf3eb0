(()=>{var e={};e.id=454,e.ids=[454],e.modules={846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1550:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},1782:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>x,tree:()=>d});var r=s(5239),a=s(8088),n=s(8170),i=s.n(n),o=s(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let d={children:["",{children:["register",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,6493)),"D:\\myaitts\\frontend\\app\\register\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,8014)),"D:\\myaitts\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\myaitts\\frontend\\app\\register\\page.tsx"],m={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/register/page",pathname:"/register",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3613:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},3723:(e,t,s)=>{"use strict";s.d(t,{S:()=>A});var r=s(687),a=s(3210),n=s(8599),i=s(1273),o=s(569),l=s(5551),d=s(3721),c=s(8853),m=s(6059),x=s(4163),u="Checkbox",[f,b]=(0,i.A)(u),[p,g]=f(u),h=a.forwardRef((e,t)=>{let{__scopeCheckbox:s,name:i,checked:d,defaultChecked:c,required:m,disabled:u,value:f="on",onCheckedChange:b,form:g,...h}=e,[j,y]=a.useState(null),k=(0,n.s)(t,e=>y(e)),C=a.useRef(!1),A=!j||g||!!j.closest("form"),[E=!1,S]=(0,l.i)({prop:d,defaultProp:c,onChange:b}),R=a.useRef(E);return a.useEffect(()=>{let e=j?.form;if(e){let t=()=>S(R.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[j,S]),(0,r.jsxs)(p,{scope:s,state:E,disabled:u,children:[(0,r.jsx)(x.sG.button,{type:"button",role:"checkbox","aria-checked":w(E)?"mixed":E,"aria-required":m,"data-state":N(E),"data-disabled":u?"":void 0,disabled:u,value:f,...h,ref:k,onKeyDown:(0,o.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.m)(e.onClick,e=>{S(e=>!!w(e)||!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,r.jsx)(v,{control:j,bubbles:!C.current,name:i,value:f,checked:E,required:m,disabled:u,form:g,style:{transform:"translateX(-100%)"},defaultChecked:!w(c)&&c})]})});h.displayName=u;var j="CheckboxIndicator",y=a.forwardRef((e,t)=>{let{__scopeCheckbox:s,forceMount:a,...n}=e,i=g(j,s);return(0,r.jsx)(m.C,{present:a||w(i.state)||!0===i.state,children:(0,r.jsx)(x.sG.span,{"data-state":N(i.state),"data-disabled":i.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});y.displayName=j;var v=e=>{let{control:t,checked:s,bubbles:n=!0,defaultChecked:i,...o}=e,l=a.useRef(null),m=(0,d.Z)(s),x=(0,c.X)(t);a.useEffect(()=>{let e=l.current,t=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==s&&t){let r=new Event("click",{bubbles:n});e.indeterminate=w(s),t.call(e,!w(s)&&s),e.dispatchEvent(r)}},[m,s,n]);let u=a.useRef(!w(s)&&s);return(0,r.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:i??u.current,...o,tabIndex:-1,ref:l,style:{...e.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function w(e){return"indeterminate"===e}function N(e){return w(e)?"indeterminate":e?"checked":"unchecked"}var k=s(3964),C=s(6241);let A=a.forwardRef(({className:e,...t},s)=>(0,r.jsx)(h,{ref:s,className:(0,C.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",e),...t,children:(0,r.jsx)(y,{className:(0,C.cn)("flex items-center justify-center text-current"),children:(0,r.jsx)(k.A,{className:"h-4 w-4"})})}));A.displayName=h.displayName},3873:e=>{"use strict";e.exports=require("path")},4021:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},5956:(e,t,s)=>{Promise.resolve().then(s.bind(s,6493))},6136:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>w});var r=s(687),a=s(6180),n=s.n(a),i=s(3210),o=s(4934),l=s(5192),d=s(8988),c=s(3723),m=s(5336),x=s(3259),u=s(3613),f=s(8869),b=s(1550),p=s(4021),g=s(2597),h=s(3861),j=s(7900);let y=(0,s(2688).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var v=s(4426);function w(){let[e,t]=(0,i.useState)({username:"",email:"",password:"",confirmPassword:"",agreeToTerms:!1,subscribeNewsletter:!1}),[s,a]=(0,i.useState)(!1),[w,N]=(0,i.useState)(!1),[k,C]=(0,i.useState)(!1),[A,E]=(0,i.useState)({}),[S,R]=(0,i.useState)(""),[P,z]=(0,i.useState)(!1),[T,$]=(0,i.useState)(0),[_,D]=(0,i.useState)(!1),[F,V]=(0,i.useState)(!1),[q,M]=(0,i.useState)(""),[I,L]=(0,i.useState)(!1),G=function(e={}){let[t,s]=(0,i.useState)({isSending:!1,sendError:null,isCodeSent:!1,isVerifying:!1,verifyError:null,countdown:0,canResend:!0,pendingRegistration:null}),r=(0,i.useCallback)(async t=>{s(e=>({...e,isSending:!0,sendError:null}));try{let e=await v.j2.sendVerificationCode(t);return s(e=>({...e,isSending:!1,isCodeSent:!0,countdown:60,canResend:!1,pendingRegistration:{email:t.email,username:t.username,password:t.password}})),e}catch(r){let t=r instanceof Error?r.message:"发送验证码失败";throw s(e=>({...e,isSending:!1,sendError:t})),e.onError?.(t),r}},[e]),a=(0,i.useCallback)(async()=>{if(t.canResend&&t.pendingRegistration)return await r(t.pendingRegistration)},[t.canResend,t.pendingRegistration,r]),n=(0,i.useCallback)(async r=>{if(!t.pendingRegistration)throw Error("没有待验证的注册信息");s(e=>({...e,isVerifying:!0,verifyError:null}));try{let a={username:t.pendingRegistration.username,email:t.pendingRegistration.email,code:r.trim()},n=await v.j2.verifyEmailAndRegister(a);return s(e=>({...e,isVerifying:!1,pendingRegistration:null})),e.onSuccess?.(),n}catch(r){let t=r instanceof Error?r.message:"验证失败";throw s(e=>({...e,isVerifying:!1,verifyError:t})),e.onError?.(t),r}},[t.pendingRegistration,e]),o=(0,i.useCallback)(()=>{s(e=>({...e,sendError:null,verifyError:null}))},[]),l=(0,i.useCallback)(()=>{s({isSending:!1,sendError:null,isCodeSent:!1,isVerifying:!1,verifyError:null,countdown:0,canResend:!0,pendingRegistration:null})},[]),d=(0,i.useCallback)(()=>t.countdown>0?`${t.countdown}秒后可重新发送`:"重新发送验证码",[t.countdown]);return{...t,sendVerificationCode:r,resendVerificationCode:a,verifyEmailAndRegister:n,clearErrors:o,reset:l,getCountdownText:d,hasError:!!(t.sendError||t.verifyError),isLoading:t.isSending||t.isVerifying}}({onSuccess:()=>{D(!0),setTimeout(()=>{window.location.href="/"},2e3)},onError:e=>{R(e)}}),Z=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),O=e=>/^[a-zA-Z0-9_]{3,20}$/.test(e),K=e=>e.length>=8&&/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(e),X=()=>{let t={};return e.username.trim()?O(e.username)||(t.username="用户名只能包含字母、数字和下划线，长度3-20位"):t.username="用户名不能为空",e.email.trim()?Z(e.email)||(t.email="请输入有效的邮箱地址"):t.email="邮箱地址不能为空",e.password?K(e.password)||(t.password="密码至少8位，包含大小写字母和数字"):t.password="密码不能为空",e.confirmPassword?e.password!==e.confirmPassword&&(t.confirmPassword="两次输入的密码不一致"):t.confirmPassword="请确认密码",e.agreeToTerms||(t.agreeToTerms="请同意服务条款和隐私政策"),E(t),0===Object.keys(t).length},H=(e,s)=>{t(t=>({...t,[e]:s})),A[e]&&E(t=>({...t,[e]:""})),S&&R("")},W=async t=>{if(t.preventDefault(),X()){if(I){if(!q.trim()){R("请输入验证码");return}C(!0),R("");try{await G.verifyEmailAndRegister(q)}catch(e){}finally{C(!1)}}else{C(!0),R("");try{await G.sendVerificationCode({email:e.email,username:e.username,password:e.password}),L(!0)}catch(e){}finally{C(!1)}}}},B=[{left:18,top:22,duration:10},{left:72,top:38,duration:12},{left:42,top:62,duration:9},{left:82,top:16,duration:11},{left:28,top:78,duration:8},{left:62,top:48,duration:13},{left:52,top:32,duration:10},{left:38,top:82,duration:12}],U=()=>(0,r.jsx)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:B.map((e,t)=>(0,r.jsx)("div",{className:"absolute w-2 h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-20 animate-float",style:{left:`${e.left}%`,top:`${e.top}%`,animationDelay:`${2*t}s`,animationDuration:`${e.duration}s`}},t))});return _?(0,r.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20 flex items-center justify-center p-6 relative overflow-hidden",children:[F&&(0,r.jsx)(U,{}),(0,r.jsx)("div",{className:"w-full max-w-md",children:(0,r.jsx)(l.Zp,{className:"border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden",children:(0,r.jsxs)(l.Wu,{className:"p-10 text-center",children:[(0,r.jsx)("div",{className:"flex justify-center mb-6",children:(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-full blur-lg opacity-50 animate-pulse"}),(0,r.jsx)("div",{className:"relative p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full shadow-xl",children:(0,r.jsx)(m.A,{className:"w-8 h-8 text-white"})})]})}),(0,r.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4",children:"注册成功！"}),(0,r.jsx)("p",{className:"text-gray-600 text-lg mb-6",children:"欢迎加入 AI 语音工作室，正在为您跳转到主页面..."}),(0,r.jsx)("div",{className:"flex justify-center",children:(0,r.jsx)("div",{className:"w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full animate-spin"})})]})})})]}):(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20 flex items-center justify-center p-6 relative overflow-hidden",children:[F&&(0,r.jsx)(U,{className:"jsx-3e1eea4381e0e46b"}),(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-green-200/20 to-blue-200/20 rounded-full blur-3xl animate-pulse"}),(0,r.jsx)("div",{style:{animationDelay:"2s"},className:"jsx-3e1eea4381e0e46b absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/20 to-purple-200/20 rounded-full blur-3xl animate-pulse"}),(0,r.jsxs)("div",{className:`jsx-3e1eea4381e0e46b w-full max-w-2xl transition-all duration-1000 ${P?"opacity-100 translate-y-0":"opacity-0 translate-y-8"}`,children:[(0,r.jsxs)(l.Zp,{className:"border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5"}),(0,r.jsxs)(l.aR,{className:"text-center pb-8 relative",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b flex justify-center mb-6",children:(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl blur-lg opacity-50 animate-pulse"}),(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b relative p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl shadow-xl",children:(0,r.jsx)(x.A,{className:"w-8 h-8 text-white"})})]})}),(0,r.jsx)(l.ZB,{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 via-green-800 to-blue-800 bg-clip-text text-transparent mb-2",children:"创建账户"}),(0,r.jsx)("p",{className:"jsx-3e1eea4381e0e46b text-gray-600 text-lg",children:"加入 AI 语音工作室，开启您的创作之旅"})]}),(0,r.jsxs)(l.Wu,{className:"relative",children:[(0,r.jsxs)("form",{onSubmit:W,className:"jsx-3e1eea4381e0e46b space-y-6",children:[(S||G.hasError)&&(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3 animate-fade-in",children:[(0,r.jsx)(u.A,{className:"w-5 h-5 text-red-500 flex-shrink-0"}),(0,r.jsx)("p",{className:"jsx-3e1eea4381e0e46b text-red-700 text-sm",children:S||G.sendError||G.verifyError})]}),G.isCodeSent&&!G.hasError&&(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3 animate-fade-in",children:[(0,r.jsx)(m.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,r.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-green-700 text-sm",children:["验证码已发送到 ",G.pendingRegistration?.email,"，请查收邮件"]})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,r.jsxs)("label",{htmlFor:"username",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["用户名 ",(0,r.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(f.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)(d.p,{id:"username",type:"text",value:e.username,onChange:e=>H("username",e.target.value),placeholder:"请输入用户名",className:`pl-10 h-12 text-lg border-2 transition-all duration-300 ${A.username?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"}`,disabled:k})]}),A.username&&(0,r.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),A.username]})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,r.jsxs)("label",{htmlFor:"email",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["邮箱地址 ",(0,r.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)(d.p,{id:"email",type:"email",value:e.email,onChange:e=>H("email",e.target.value),placeholder:"请输入邮箱地址",className:`pl-10 h-12 text-lg border-2 transition-all duration-300 ${A.email?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"}`,disabled:k})]}),A.email&&(0,r.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),A.email]})]})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,r.jsxs)("label",{htmlFor:"password",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["密码 ",(0,r.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)(d.p,{id:"password",type:s?"text":"password",value:e.password,onChange:e=>H("password",e.target.value),placeholder:"请输入密码",className:`pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ${A.password?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"}`,disabled:k}),(0,r.jsx)("button",{type:"button",onClick:()=>a(!s),disabled:k,className:"jsx-3e1eea4381e0e46b absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200",children:s?(0,r.jsx)(g.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,r.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),e.password&&(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center gap-2",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b flex-1 bg-gray-200 rounded-full h-2 overflow-hidden",children:(0,r.jsx)("div",{style:{width:`${T/5*100}%`},className:`jsx-3e1eea4381e0e46b h-full transition-all duration-300 ${T<=1?"bg-red-500":T<=2?"bg-orange-500":T<=3?"bg-yellow-500":T<=4?"bg-blue-500":"bg-green-500"}`})}),(0,r.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-sm font-medium text-gray-600",children:T<=1?"弱":T<=2?"一般":T<=3?"中等":T<=4?"强":"很强"})]})}),A.password&&(0,r.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),A.password]})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,r.jsxs)("label",{htmlFor:"confirmPassword",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["确认密码 ",(0,r.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(p.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)(d.p,{id:"confirmPassword",type:w?"text":"password",value:e.confirmPassword,onChange:e=>H("confirmPassword",e.target.value),placeholder:"请再次输入密码",className:`pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ${A.confirmPassword?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"}`,disabled:k}),(0,r.jsx)("button",{type:"button",onClick:()=>N(!w),disabled:k,className:"jsx-3e1eea4381e0e46b absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200",children:w?(0,r.jsx)(g.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,r.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),A.confirmPassword&&(0,r.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),A.confirmPassword]})]}),I&&(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,r.jsxs)("label",{htmlFor:"verificationCode",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["邮箱验证码 ",(0,r.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,r.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})}),(0,r.jsx)(d.p,{id:"verificationCode",type:"text",value:q,onChange:e=>M(e.target.value),placeholder:"请输入6位验证码",className:"pl-10 h-12 text-lg border-2 transition-all duration-300 border-gray-200 focus:border-green-400 focus:ring-green-100",disabled:k,maxLength:6})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center justify-between",children:[(0,r.jsx)(o.$,{type:"button",variant:"ghost",onClick:()=>{L(!1),M(""),G.reset(),R("")},className:"text-gray-600 hover:text-gray-800",disabled:k,children:"← 返回修改信息"}),(0,r.jsx)(o.$,{type:"button",variant:"outline",onClick:G.resendVerificationCode,disabled:!G.canResend||G.isSending,className:"text-sm",children:G.isSending?(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center gap-2",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"}),"发送中..."]}):G.getCountdownText()})]})]}),!I&&(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-4",children:[(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-start space-x-3",children:[(0,r.jsx)(c.S,{id:"agreeToTerms",checked:e.agreeToTerms,onCheckedChange:e=>H("agreeToTerms",e),disabled:k,className:"data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 mt-1"}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex-1",children:[(0,r.jsxs)("label",{htmlFor:"agreeToTerms",className:"jsx-3e1eea4381e0e46b text-sm text-gray-700 cursor-pointer select-none leading-relaxed",children:["我已阅读并同意"," ",(0,r.jsx)("button",{type:"button",onClick:()=>alert("服务条款页面"),className:"jsx-3e1eea4381e0e46b text-green-600 hover:text-green-800 hover:underline font-semibold",children:"服务条款"})," ","和"," ",(0,r.jsx)("button",{type:"button",onClick:()=>alert("隐私政策页面"),className:"jsx-3e1eea4381e0e46b text-green-600 hover:text-green-800 hover:underline font-semibold",children:"隐私政策"})," ",(0,r.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),A.agreeToTerms&&(0,r.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 mt-2 animate-fade-in",children:[(0,r.jsx)(u.A,{className:"w-4 h-4"}),A.agreeToTerms]})]})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center space-x-3",children:[(0,r.jsx)(c.S,{id:"subscribeNewsletter",checked:e.subscribeNewsletter,onCheckedChange:e=>H("subscribeNewsletter",e),disabled:k,className:"data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"}),(0,r.jsx)("label",{htmlFor:"subscribeNewsletter",className:"jsx-3e1eea4381e0e46b text-sm text-gray-700 cursor-pointer select-none",children:"订阅产品更新和优惠信息"})]})]}),(0,r.jsxs)(o.$,{type:"submit",disabled:k||I&&!q.trim(),className:"w-full h-12 text-lg font-bold bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 hover:from-green-600 hover:via-blue-600 hover:to-purple-600 disabled:from-gray-400 disabled:to-gray-500 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 relative overflow-hidden group",children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b relative z-10 flex items-center justify-center gap-3",children:k?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),I?"验证中...":"发送验证码中..."]}):(0,r.jsx)(r.Fragment,{children:I?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(m.A,{className:"w-5 h-5"}),"完成注册"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(j.A,{className:"w-5 h-5"}),"发送验证码"]})})})]}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-start gap-3",children:[(0,r.jsx)(y,{className:"w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5"}),(0,r.jsxs)("div",{className:"jsx-3e1eea4381e0e46b text-sm text-blue-800",children:[(0,r.jsx)("p",{className:"jsx-3e1eea4381e0e46b font-semibold mb-1",children:"安全提示"}),(0,r.jsx)("p",{className:"jsx-3e1eea4381e0e46b",children:"您的个人信息将被安全加密存储，我们承诺不会向第三方泄露您的隐私信息。"})]})]})]}),(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b mt-8 text-center",children:(0,r.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-gray-600",children:["已有账户？"," ",(0,r.jsx)("button",{onClick:()=>window.location.href="/login",className:"jsx-3e1eea4381e0e46b text-green-600 hover:text-green-800 font-semibold hover:underline transition-colors duration-200",children:"立即登录"})]})})]})]}),(0,r.jsx)("div",{className:"jsx-3e1eea4381e0e46b mt-6 text-center",children:(0,r.jsx)("button",{onClick:()=>window.location.href="/",className:"jsx-3e1eea4381e0e46b text-gray-600 hover:text-gray-800 text-sm hover:underline transition-colors duration-200",children:"← 返回首页"})})]}),(0,r.jsx)(n(),{id:"3e1eea4381e0e46b",children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-webkit-keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fade-in{from{opacity:0;-moz-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fade-in{from{opacity:0;-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}.animate-float.jsx-3e1eea4381e0e46b{-webkit-animation:float 8s ease-in-out infinite;-moz-animation:float 8s ease-in-out infinite;-o-animation:float 8s ease-in-out infinite;animation:float 8s ease-in-out infinite}.animate-fade-in.jsx-3e1eea4381e0e46b{-webkit-animation:fade-in.3s ease-out forwards;-moz-animation:fade-in.3s ease-out forwards;-o-animation:fade-in.3s ease-out forwards;animation:fade-in.3s ease-out forwards}.animate-float.jsx-3e1eea4381e0e46b{will-change:transform;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.animate-fade-in.jsx-3e1eea4381e0e46b{will-change:transform,opacity;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}"})]})}},6493:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\myaitts\\\\frontend\\\\app\\\\register\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\myaitts\\frontend\\app\\register\\page.tsx","default")},7900:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},8869:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(2688).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},8988:(e,t,s)=>{"use strict";s.d(t,{p:()=>i});var r=s(687),a=s(3210),n=s(6241);let i=a.forwardRef(({className:e,type:t,...s},a)=>(0,r.jsx)("input",{type:t,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",e),ref:a,...s}));i.displayName="Input"},9108:(e,t,s)=>{Promise.resolve().then(s.bind(s,6136))},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[995,574,625],()=>s(1782));module.exports=r})();