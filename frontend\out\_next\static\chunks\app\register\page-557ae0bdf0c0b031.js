(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[454],{1007:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2486:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2919:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3695:(e,a,s)=>{"use strict";s.d(a,{S:()=>A});var t=s(5155),r=s(2115),n=s(6101),l=s(6081),i=s(5185),o=s(5845),d=s(5503),c=s(1275),m=s(8905),x=s(3655),b="Checkbox",[u,f]=(0,l.A)(b),[g,h]=u(b),p=r.forwardRef((e,a)=>{let{__scopeCheckbox:s,name:l,checked:d,defaultChecked:c,required:m,disabled:b,value:u="on",onCheckedChange:f,form:h,...p}=e,[j,y]=r.useState(null),k=(0,n.s)(a,e=>y(e)),C=r.useRef(!1),A=!j||h||!!j.closest("form"),[E=!1,S]=(0,o.i)({prop:d,defaultProp:c,onChange:f}),R=r.useRef(E);return r.useEffect(()=>{let e=null==j?void 0:j.form;if(e){let a=()=>S(R.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[j,S]),(0,t.jsxs)(g,{scope:s,state:E,disabled:b,children:[(0,t.jsx)(x.sG.button,{type:"button",role:"checkbox","aria-checked":w(E)?"mixed":E,"aria-required":m,"data-state":N(E),"data-disabled":b?"":void 0,disabled:b,value:u,...p,ref:k,onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(e.onClick,e=>{S(e=>!!w(e)||!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,t.jsx)(v,{control:j,bubbles:!C.current,name:l,value:u,checked:E,required:m,disabled:b,form:h,style:{transform:"translateX(-100%)"},defaultChecked:!w(c)&&c})]})});p.displayName=b;var j="CheckboxIndicator",y=r.forwardRef((e,a)=>{let{__scopeCheckbox:s,forceMount:r,...n}=e,l=h(j,s);return(0,t.jsx)(m.C,{present:r||w(l.state)||!0===l.state,children:(0,t.jsx)(x.sG.span,{"data-state":N(l.state),"data-disabled":l.disabled?"":void 0,...n,ref:a,style:{pointerEvents:"none",...e.style}})})});y.displayName=j;var v=e=>{let{control:a,checked:s,bubbles:n=!0,defaultChecked:l,...i}=e,o=r.useRef(null),m=(0,d.Z)(s),x=(0,c.X)(a);r.useEffect(()=>{let e=o.current,a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==s&&a){let t=new Event("click",{bubbles:n});e.indeterminate=w(s),a.call(e,!w(s)&&s),e.dispatchEvent(t)}},[m,s,n]);let b=r.useRef(!w(s)&&s);return(0,t.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=l?l:b.current,...i,tabIndex:-1,ref:o,style:{...e.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function w(e){return"indeterminate"===e}function N(e){return w(e)?"indeterminate":e?"checked":"unchecked"}var k=s(5196),C=s(3999);let A=r.forwardRef((e,a)=>{let{className:s,...r}=e;return(0,t.jsx)(p,{ref:a,className:(0,C.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",s),...r,children:(0,t.jsx)(y,{className:(0,C.cn)("flex items-center justify-center text-current"),children:(0,t.jsx)(k.A,{className:"h-4 w-4"})})})});A.displayName=p.displayName},5339:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},7190:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>w});var t=s(5155),r=s(9137),n=s.n(r),l=s(2115),i=s(7168),o=s(8482),d=s(9852),c=s(3695),m=s(646),x=s(9588),b=s(5339),u=s(1007),f=s(8883),g=s(2919),h=s(8749),p=s(2657),j=s(2486);let y=(0,s(9946).A)("Shield",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}]]);var v=s(6194);function w(){var e;let[a,s]=(0,l.useState)({username:"",email:"",password:"",confirmPassword:"",agreeToTerms:!1,subscribeNewsletter:!1}),[r,w]=(0,l.useState)(!1),[N,k]=(0,l.useState)(!1),[C,A]=(0,l.useState)(!1),[E,S]=(0,l.useState)({}),[R,T]=(0,l.useState)(""),[z,P]=(0,l.useState)(!1),[F,V]=(0,l.useState)(0),[D,Z]=(0,l.useState)(!1),[L,_]=(0,l.useState)(!1),[M,I]=(0,l.useState)(""),[O,$]=(0,l.useState)(!1),q=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[a,s]=(0,l.useState)({isSending:!1,sendError:null,isCodeSent:!1,isVerifying:!1,verifyError:null,countdown:0,canResend:!0,pendingRegistration:null});(0,l.useEffect)(()=>{let e;return a.countdown>0&&(e=setTimeout(()=>{s(e=>({...e,countdown:e.countdown-1,canResend:e.countdown<=1}))},1e3)),()=>{e&&clearTimeout(e)}},[a.countdown]);let t=(0,l.useCallback)(async a=>{s(e=>({...e,isSending:!0,sendError:null}));try{let e=await v.j2.sendVerificationCode(a);return s(e=>({...e,isSending:!1,isCodeSent:!0,countdown:60,canResend:!1,pendingRegistration:{email:a.email,username:a.username,password:a.password}})),e}catch(r){var t;let a=r instanceof Error?r.message:"发送验证码失败";throw s(e=>({...e,isSending:!1,sendError:a})),null===(t=e.onError)||void 0===t||t.call(e,a),r}},[e]),r=(0,l.useCallback)(async()=>{if(a.canResend&&a.pendingRegistration)return await t(a.pendingRegistration)},[a.canResend,a.pendingRegistration,t]),n=(0,l.useCallback)(async t=>{var r,n;if(!a.pendingRegistration)throw Error("没有待验证的注册信息");s(e=>({...e,isVerifying:!0,verifyError:null}));try{let n={username:a.pendingRegistration.username,email:a.pendingRegistration.email,code:t.trim()},l=await v.j2.verifyEmailAndRegister(n);return s(e=>({...e,isVerifying:!1,pendingRegistration:null})),null===(r=e.onSuccess)||void 0===r||r.call(e),l}catch(t){let a=t instanceof Error?t.message:"验证失败";throw s(e=>({...e,isVerifying:!1,verifyError:a})),null===(n=e.onError)||void 0===n||n.call(e,a),t}},[a.pendingRegistration,e]),i=(0,l.useCallback)(()=>{s(e=>({...e,sendError:null,verifyError:null}))},[]),o=(0,l.useCallback)(()=>{s({isSending:!1,sendError:null,isCodeSent:!1,isVerifying:!1,verifyError:null,countdown:0,canResend:!0,pendingRegistration:null})},[]),d=(0,l.useCallback)(()=>a.countdown>0?"".concat(a.countdown,"秒后可重新发送"):"重新发送验证码",[a.countdown]);return{...a,sendVerificationCode:t,resendVerificationCode:r,verifyEmailAndRegister:n,clearErrors:i,reset:o,getCountdownText:d,hasError:!!(a.sendError||a.verifyError),isLoading:a.isSending||a.isVerifying}}({onSuccess:()=>{Z(!0),setTimeout(()=>{window.location.href="/"},2e3)},onError:e=>{T(e)}});(0,l.useEffect)(()=>{_(!0)},[]),(0,l.useEffect)(()=>{P(!0)},[]),(0,l.useEffect)(()=>{var e;let s;V((e=a.password,s=0,e.length>=8&&(s+=1),/[a-z]/.test(e)&&(s+=1),/[A-Z]/.test(e)&&(s+=1),/[0-9]/.test(e)&&(s+=1),/[^A-Za-z0-9]/.test(e)&&(s+=1),s))},[a.password]);let G=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),H=e=>/^[a-zA-Z0-9_]{3,20}$/.test(e),K=e=>e.length>=8&&/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(e),W=()=>{let e={};return a.username.trim()?H(a.username)||(e.username="用户名只能包含字母、数字和下划线，长度3-20位"):e.username="用户名不能为空",a.email.trim()?G(a.email)||(e.email="请输入有效的邮箱地址"):e.email="邮箱地址不能为空",a.password?K(a.password)||(e.password="密码至少8位，包含大小写字母和数字"):e.password="密码不能为空",a.confirmPassword?a.password!==a.confirmPassword&&(e.confirmPassword="两次输入的密码不一致"):e.confirmPassword="请确认密码",a.agreeToTerms||(e.agreeToTerms="请同意服务条款和隐私政策"),S(e),0===Object.keys(e).length},X=(e,a)=>{s(s=>({...s,[e]:a})),E[e]&&S(a=>({...a,[e]:""})),R&&T("")},B=async e=>{if(e.preventDefault(),W()){if(O){if(!M.trim()){T("请输入验证码");return}A(!0),T("");try{await q.verifyEmailAndRegister(M)}catch(e){}finally{A(!1)}}else{A(!0),T("");try{await q.sendVerificationCode({email:a.email,username:a.username,password:a.password}),$(!0)}catch(e){}finally{A(!1)}}}},U=[{left:18,top:22,duration:10},{left:72,top:38,duration:12},{left:42,top:62,duration:9},{left:82,top:16,duration:11},{left:28,top:78,duration:8},{left:62,top:48,duration:13},{left:52,top:32,duration:10},{left:38,top:82,duration:12}],J=()=>(0,t.jsx)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:U.map((e,a)=>(0,t.jsx)("div",{className:"absolute w-2 h-2 bg-gradient-to-r from-green-400 to-blue-400 rounded-full opacity-20 animate-float",style:{left:"".concat(e.left,"%"),top:"".concat(e.top,"%"),animationDelay:"".concat(2*a,"s"),animationDuration:"".concat(e.duration,"s")}},a))});return D?(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20 flex items-center justify-center p-6 relative overflow-hidden",children:[L&&(0,t.jsx)(J,{}),(0,t.jsx)("div",{className:"w-full max-w-md",children:(0,t.jsx)(o.Zp,{className:"border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden",children:(0,t.jsxs)(o.Wu,{className:"p-10 text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-6",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-full blur-lg opacity-50 animate-pulse"}),(0,t.jsx)("div",{className:"relative p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-full shadow-xl",children:(0,t.jsx)(m.A,{className:"w-8 h-8 text-white"})})]})}),(0,t.jsx)("h2",{className:"text-3xl font-bold bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent mb-4",children:"注册成功！"}),(0,t.jsx)("p",{className:"text-gray-600 text-lg mb-6",children:"欢迎加入 AI 语音工作室，正在为您跳转到主页面..."}),(0,t.jsx)("div",{className:"flex justify-center",children:(0,t.jsx)("div",{className:"w-8 h-8 border-4 border-green-500 border-t-transparent rounded-full animate-spin"})})]})})})]}):(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b min-h-screen bg-gradient-to-br from-slate-50 via-green-50/30 to-blue-50/20 flex items-center justify-center p-6 relative overflow-hidden",children:[L&&(0,t.jsx)(J,{className:"jsx-3e1eea4381e0e46b"}),(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-green-200/20 to-blue-200/20 rounded-full blur-3xl animate-pulse"}),(0,t.jsx)("div",{style:{animationDelay:"2s"},className:"jsx-3e1eea4381e0e46b absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-blue-200/20 to-purple-200/20 rounded-full blur-3xl animate-pulse"}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b "+"w-full max-w-2xl transition-all duration-1000 ".concat(z?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:[(0,t.jsxs)(o.Zp,{className:"border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-green-500/5 to-blue-500/5"}),(0,t.jsxs)(o.aR,{className:"text-center pb-8 relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b flex justify-center mb-6",children:(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl blur-lg opacity-50 animate-pulse"}),(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b relative p-4 bg-gradient-to-r from-green-500 to-blue-600 rounded-2xl shadow-xl",children:(0,t.jsx)(x.A,{className:"w-8 h-8 text-white"})})]})}),(0,t.jsx)(o.ZB,{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 via-green-800 to-blue-800 bg-clip-text text-transparent mb-2",children:"创建账户"}),(0,t.jsx)("p",{className:"jsx-3e1eea4381e0e46b text-gray-600 text-lg",children:"加入 AI 语音工作室，开启您的创作之旅"})]}),(0,t.jsxs)(o.Wu,{className:"relative",children:[(0,t.jsxs)("form",{onSubmit:B,className:"jsx-3e1eea4381e0e46b space-y-6",children:[(R||q.hasError)&&(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-5 h-5 text-red-500 flex-shrink-0"}),(0,t.jsx)("p",{className:"jsx-3e1eea4381e0e46b text-red-700 text-sm",children:R||q.sendError||q.verifyError})]}),q.isCodeSent&&!q.hasError&&(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b p-4 bg-green-50 border border-green-200 rounded-lg flex items-center gap-3 animate-fade-in",children:[(0,t.jsx)(m.A,{className:"w-5 h-5 text-green-500 flex-shrink-0"}),(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-green-700 text-sm",children:["验证码已发送到 ",null===(e=q.pendingRegistration)||void 0===e?void 0:e.email,"，请查收邮件"]})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,t.jsxs)("label",{htmlFor:"username",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["用户名 ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(u.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)(d.p,{id:"username",type:"text",value:a.username,onChange:e=>X("username",e.target.value),placeholder:"请输入用户名",className:"pl-10 h-12 text-lg border-2 transition-all duration-300 ".concat(E.username?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"),disabled:C})]}),E.username&&(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),E.username]})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,t.jsxs)("label",{htmlFor:"email",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["邮箱地址 ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(f.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)(d.p,{id:"email",type:"email",value:a.email,onChange:e=>X("email",e.target.value),placeholder:"请输入邮箱地址",className:"pl-10 h-12 text-lg border-2 transition-all duration-300 ".concat(E.email?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"),disabled:C})]}),E.email&&(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),E.email]})]})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,t.jsxs)("label",{htmlFor:"password",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["密码 ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(g.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)(d.p,{id:"password",type:r?"text":"password",value:a.password,onChange:e=>X("password",e.target.value),placeholder:"请输入密码",className:"pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ".concat(E.password?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"),disabled:C}),(0,t.jsx)("button",{type:"button",onClick:()=>w(!r),disabled:C,className:"jsx-3e1eea4381e0e46b absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200",children:r?(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,t.jsx)(p.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),a.password&&(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center gap-2",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b flex-1 bg-gray-200 rounded-full h-2 overflow-hidden",children:(0,t.jsx)("div",{style:{width:"".concat(F/5*100,"%")},className:"jsx-3e1eea4381e0e46b "+"h-full transition-all duration-300 ".concat(F<=1?"bg-red-500":F<=2?"bg-orange-500":F<=3?"bg-yellow-500":F<=4?"bg-blue-500":"bg-green-500")})}),(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-sm font-medium text-gray-600",children:F<=1?"弱":F<=2?"一般":F<=3?"中等":F<=4?"强":"很强"})]})}),E.password&&(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),E.password]})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,t.jsxs)("label",{htmlFor:"confirmPassword",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["确认密码 ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(g.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)(d.p,{id:"confirmPassword",type:N?"text":"password",value:a.confirmPassword,onChange:e=>X("confirmPassword",e.target.value),placeholder:"请再次输入密码",className:"pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ".concat(E.confirmPassword?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-green-400 focus:ring-green-100"),disabled:C}),(0,t.jsx)("button",{type:"button",onClick:()=>k(!N),disabled:C,className:"jsx-3e1eea4381e0e46b absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200",children:N?(0,t.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,t.jsx)(p.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),E.confirmPassword&&(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),E.confirmPassword]})]}),O&&(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,t.jsxs)("label",{htmlFor:"verificationCode",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:["邮箱验证码 ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,t.jsx)(f.A,{className:"h-5 w-5 text-gray-400"})}),(0,t.jsx)(d.p,{id:"verificationCode",type:"text",value:M,onChange:e=>I(e.target.value),placeholder:"请输入6位验证码",className:"pl-10 h-12 text-lg border-2 transition-all duration-300 border-gray-200 focus:border-green-400 focus:ring-green-100",disabled:C,maxLength:6})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center justify-between",children:[(0,t.jsx)(i.$,{type:"button",variant:"ghost",onClick:()=>{$(!1),I(""),q.reset(),T("")},className:"text-gray-600 hover:text-gray-800",disabled:C,children:"← 返回修改信息"}),(0,t.jsx)(i.$,{type:"button",variant:"outline",onClick:q.resendVerificationCode,disabled:!q.canResend||q.isSending,className:"text-sm",children:q.isSending?(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center gap-2",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b w-4 h-4 border-2 border-green-500 border-t-transparent rounded-full animate-spin"}),"发送中..."]}):q.getCountdownText()})]})]}),!O&&(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-4",children:[(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-start space-x-3",children:[(0,t.jsx)(c.S,{id:"agreeToTerms",checked:a.agreeToTerms,onCheckedChange:e=>X("agreeToTerms",e),disabled:C,className:"data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500 mt-1"}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex-1",children:[(0,t.jsxs)("label",{htmlFor:"agreeToTerms",className:"jsx-3e1eea4381e0e46b text-sm text-gray-700 cursor-pointer select-none leading-relaxed",children:["我已阅读并同意"," ",(0,t.jsx)("button",{type:"button",onClick:()=>alert("服务条款页面"),className:"jsx-3e1eea4381e0e46b text-green-600 hover:text-green-800 hover:underline font-semibold",children:"服务条款"})," ","和"," ",(0,t.jsx)("button",{type:"button",onClick:()=>alert("隐私政策页面"),className:"jsx-3e1eea4381e0e46b text-green-600 hover:text-green-800 hover:underline font-semibold",children:"隐私政策"})," ",(0,t.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-red-500",children:"*"})]}),E.agreeToTerms&&(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 mt-2 animate-fade-in",children:[(0,t.jsx)(b.A,{className:"w-4 h-4"}),E.agreeToTerms]})]})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center space-x-3",children:[(0,t.jsx)(c.S,{id:"subscribeNewsletter",checked:a.subscribeNewsletter,onCheckedChange:e=>X("subscribeNewsletter",e),disabled:C,className:"data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"}),(0,t.jsx)("label",{htmlFor:"subscribeNewsletter",className:"jsx-3e1eea4381e0e46b text-sm text-gray-700 cursor-pointer select-none",children:"订阅产品更新和优惠信息"})]})]}),(0,t.jsxs)(i.$,{type:"submit",disabled:C||O&&!M.trim(),className:"w-full h-12 text-lg font-bold bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 hover:from-green-600 hover:via-blue-600 hover:to-purple-600 disabled:from-gray-400 disabled:to-gray-500 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 relative overflow-hidden group",children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b relative z-10 flex items-center justify-center gap-3",children:C?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),O?"验证中...":"发送验证码中..."]}):(0,t.jsx)(t.Fragment,{children:O?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(m.A,{className:"w-5 h-5"}),"完成注册"]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(j.A,{className:"w-5 h-5"}),"发送验证码"]})})})]}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b p-4 bg-blue-50 border border-blue-200 rounded-lg flex items-start gap-3",children:[(0,t.jsx)(y,{className:"w-5 h-5 text-blue-600 flex-shrink-0 mt-0.5"}),(0,t.jsxs)("div",{className:"jsx-3e1eea4381e0e46b text-sm text-blue-800",children:[(0,t.jsx)("p",{className:"jsx-3e1eea4381e0e46b font-semibold mb-1",children:"安全提示"}),(0,t.jsx)("p",{className:"jsx-3e1eea4381e0e46b",children:"您的个人信息将被安全加密存储，我们承诺不会向第三方泄露您的隐私信息。"})]})]})]}),(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b mt-8 text-center",children:(0,t.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-gray-600",children:["已有账户？"," ",(0,t.jsx)("button",{onClick:()=>window.location.href="/login",className:"jsx-3e1eea4381e0e46b text-green-600 hover:text-green-800 font-semibold hover:underline transition-colors duration-200",children:"立即登录"})]})})]})]}),(0,t.jsx)("div",{className:"jsx-3e1eea4381e0e46b mt-6 text-center",children:(0,t.jsx)("button",{onClick:()=>window.location.href="/",className:"jsx-3e1eea4381e0e46b text-gray-600 hover:text-gray-800 text-sm hover:underline transition-colors duration-200",children:"← 返回首页"})})]}),(0,t.jsx)(n(),{id:"3e1eea4381e0e46b",children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-webkit-keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fade-in{from{opacity:0;-moz-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fade-in{from{opacity:0;-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}.animate-float.jsx-3e1eea4381e0e46b{-webkit-animation:float 8s ease-in-out infinite;-moz-animation:float 8s ease-in-out infinite;-o-animation:float 8s ease-in-out infinite;animation:float 8s ease-in-out infinite}.animate-fade-in.jsx-3e1eea4381e0e46b{-webkit-animation:fade-in.3s ease-out forwards;-moz-animation:fade-in.3s ease-out forwards;-o-animation:fade-in.3s ease-out forwards;animation:fade-in.3s ease-out forwards}.animate-float.jsx-3e1eea4381e0e46b{will-change:transform;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.animate-fade-in.jsx-3e1eea4381e0e46b{will-change:transform,opacity;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}"})]})}},8883:(e,a,s)=>{"use strict";s.d(a,{A:()=>t});let t=(0,s(9946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},9470:(e,a,s)=>{Promise.resolve().then(s.bind(s,7190))}},e=>{var a=a=>e(e.s=a);e.O(0,[228,550,927,441,684,358],()=>a(9470)),_N_E=e.O()}]);