(()=>{var e={};e.id=719,e.ids=[719],e.modules={83:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},807:(e,t,s)=>{Promise.resolve().then(s.bind(s,5440))},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},2655:(e,t,s)=>{Promise.resolve().then(s.bind(s,8594))},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3873:e=>{"use strict";e.exports=require("path")},5440:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\myaitts\\\\frontend\\\\app\\\\test-auth-flow\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\myaitts\\frontend\\app\\test-auth-flow\\page.tsx","default")},5478:(e,t,s)=>{"use strict";s.d(t,{A:()=>n,tu:()=>o});let a={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"};function r(e){if(e?.code)return Object.values(a).includes(e.code);if(e?.message){let t=e.message.toLowerCase();return t.includes("token")||t.includes("expired")||t.includes("unauthorized")||t.includes("401")||t.includes("登录")||t.includes("refresh")}return!1}function o(e,t){let s=r(e);s&&t&&t();let a=r(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:e?.shouldRedirect===void 0||e.shouldRedirect}:{title:"操作失败",description:e?.message||"请检查网络连接后重试",shouldRedirect:!1};return{isAuthError:s,message:a.description,shouldRedirect:a.shouldRedirect}}function n(e){return r(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:e?.message?.includes("卡密")?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:e?.message||"请检查网络连接后重试",variant:"destructive"}}},6298:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var a=s(5239),r=s(8088),o=s(8170),n=s.n(o),i=s(893),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d={children:["",{children:["test-auth-flow",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5440)),"D:\\myaitts\\frontend\\app\\test-auth-flow\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,8014)),"D:\\myaitts\\frontend\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,5284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["D:\\myaitts\\frontend\\app\\test-auth-flow\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/test-auth-flow/page",pathname:"/test-auth-flow",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},6349:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(2688).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},7826:(e,t,s)=>{"use strict";s.d(t,{Cf:()=>u,Es:()=>m,L3:()=>x,c7:()=>p,lG:()=>l,rr:()=>f});var a=s(687),r=s(3210),o=s(8960),n=s(1860),i=s(6241);let l=o.bL;o.l9;let d=o.ZL;o.bm;let c=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(o.hJ,{ref:s,className:(0,i.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...t}));c.displayName=o.hJ.displayName;let u=r.forwardRef(({className:e,children:t,...s},r)=>(0,a.jsxs)(d,{children:[(0,a.jsx)(c,{}),(0,a.jsxs)(o.UC,{ref:r,className:(0,i.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...s,children:[t,(0,a.jsxs)(o.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,a.jsx)(n.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]}));u.displayName=o.UC.displayName;let p=({className:e,...t})=>(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col space-y-1.5 text-center sm:text-left",e),...t});p.displayName="DialogHeader";let m=({className:e,...t})=>(0,a.jsx)("div",{className:(0,i.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",e),...t});m.displayName="DialogFooter";let x=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(o.hE,{ref:s,className:(0,i.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName=o.hE.displayName;let f=r.forwardRef(({className:e,...t},s)=>(0,a.jsx)(o.VY,{ref:s,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));f.displayName=o.VY.displayName},8594:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>x});var a=s(687),r=s(3210),o=s(4934),n=s(5192),i=s(7826),l=s(4426),d=s(9556),c=s(1702),u=s(5478),p=s(6349),m=s(83);function x(){let[e,t]=(0,r.useState)(""),[s,x]=(0,r.useState)(!1),{toast:f}=(0,c.dj)(),h=async()=>{try{t("开始测试..."),d.tC.setTokens("invalid_access_token","invalid_refresh_token","<EMAIL>"),t("已设置无效token，正在调用getUserQuota...");let e=await l.j2.getUserQuota();t("意外成功: "+JSON.stringify(e))}catch(a){console.error("测试错误:",a),t(`捕获错误: ${a.message}
错误码: ${a.code||"无"}
shouldRedirect: ${a.shouldRedirect||"无"}`);let{isAuthError:e,shouldRedirect:s}=(0,u.tu)(a,()=>{console.log("认证错误回调被触发"),x(!0),f({title:"认证失败",description:"会话已过期，正在跳转到登录页面...",variant:"destructive"})});console.log("错误处理结果:",{isAuth:e,shouldRedirect:s}),e&&s&&(console.log("将在2秒后跳转到登录页面"),setTimeout(()=>{console.log("执行跳转"),window.location.href="/login"},2e3))}};return(0,a.jsxs)("div",{className:"container mx-auto p-4",children:[(0,a.jsxs)(n.Zp,{className:"max-w-2xl mx-auto",children:[(0,a.jsx)(n.aR,{children:(0,a.jsx)(n.ZB,{children:"认证流程完整测试"})}),(0,a.jsxs)(n.Wu,{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o.$,{onClick:()=>{let e=d.tC.getAccessToken(),s=d.tC.getRefreshToken(),a=d.tC.getUserEmail();t(`当前token状态:
Access Token: ${e?`${e.substring(0,20)}...`:"无"}
Refresh Token: ${s?`${s.substring(0,20)}...`:"无"}
Email: ${a||"无"}`)},variant:"outline",children:"检查当前Token状态"}),(0,a.jsx)(o.$,{onClick:h,variant:"destructive",children:"模拟Token过期（完整流程）"}),(0,a.jsx)(o.$,{onClick:()=>{d.tC.clearTokens(),t("已清除所有token"),x(!1)},variant:"secondary",children:"清除所有Token"})]}),e&&(0,a.jsxs)("div",{className:"p-4 bg-gray-100 rounded-lg",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"测试结果:"}),(0,a.jsx)("pre",{className:"text-sm whitespace-pre-wrap",children:e})]})]})]}),(0,a.jsx)(i.lG,{open:s,onOpenChange:x,children:(0,a.jsxs)(i.Cf,{className:"sm:max-w-md border-0 shadow-2xl bg-white/95 backdrop-blur-xl",children:[(0,a.jsxs)(i.c7,{className:"text-center space-y-4",children:[(0,a.jsx)("div",{className:"mx-auto w-16 h-16 bg-gradient-to-r from-blue-100 to-indigo-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(p.A,{className:"w-8 h-8 text-blue-500"})}),(0,a.jsx)(i.L3,{className:"text-center text-2xl font-bold bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent",children:"登录已过期"}),(0,a.jsx)(i.rr,{className:"text-gray-600",children:(0,a.jsx)("div",{className:"flex items-center justify-center gap-3 p-3 bg-blue-50 rounded-lg border border-blue-200",children:(0,a.jsx)("span",{className:"text-sm",children:"您的会话已过期，请重新登录以继续。"})})})]}),(0,a.jsx)(i.Es,{className:"mt-6",children:(0,a.jsxs)(o.$,{onClick:async()=>{x(!1),await l.j2.logout(),window.location.href="/login"},className:"w-full bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-300",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"重新登录"]})})]})})]})}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[995,960,625],()=>s(6298));module.exports=a})();