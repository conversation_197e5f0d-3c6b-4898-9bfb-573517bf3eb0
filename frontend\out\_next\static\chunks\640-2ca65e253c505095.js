(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[640],{64:(e,t,r)=>{"use strict";r.d(t,{UC:()=>$,B8:()=>X,bL:()=>V,l9:()=>Z});var n=r(2115),i=r(5185),o=r(6081),s=r(2284),a=r(6101),l=r(1285),u=r(3655),c=r(9033),d=r(5845),f=r(4315),h=r(5155),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},v="RovingFocusGroup",[y,_,S]=(0,s.N)(v),[g,w]=(0,o.A)(v,[S]),[b,R]=g(v),A=n.forwardRef((e,t)=>(0,h.jsx)(y.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(y.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(x,{...e,ref:t})})}));A.displayName=v;var x=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,orientation:o,loop:s=!1,dir:l,currentTabStopId:v,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:S,onEntryFocus:g,preventScrollOnEntryFocus:w=!1,...R}=e,A=n.useRef(null),x=(0,a.s)(t,A),C=(0,f.jH)(l),[F=null,k]=(0,d.i)({prop:v,defaultProp:y,onChange:S}),[z,I]=n.useState(!1),M=(0,c.c)(g),T=_(r),E=n.useRef(!1),[N,D]=n.useState(0);return n.useEffect(()=>{let e=A.current;if(e)return e.addEventListener(p,M),()=>e.removeEventListener(p,M)},[M]),(0,h.jsx)(b,{scope:r,orientation:o,dir:C,loop:s,currentTabStopId:F,onItemFocus:n.useCallback(e=>k(e),[k]),onItemShiftTab:n.useCallback(()=>I(!0),[]),onFocusableItemAdd:n.useCallback(()=>D(e=>e+1),[]),onFocusableItemRemove:n.useCallback(()=>D(e=>e-1),[]),children:(0,h.jsx)(u.sG.div,{tabIndex:z||0===N?-1:0,"data-orientation":o,...R,ref:x,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{E.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!E.current;if(e.target===e.currentTarget&&t&&!z){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=T().filter(e=>e.focusable);j([e.find(e=>e.active),e.find(e=>e.id===F),...e].filter(Boolean).map(e=>e.ref.current),w)}}E.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>I(!1))})})}),C="RovingFocusGroupItem",F=n.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:r,focusable:o=!0,active:s=!1,tabStopId:a,...c}=e,d=(0,l.B)(),f=a||d,p=R(C,r),m=p.currentTabStopId===f,v=_(r),{onFocusableItemAdd:S,onFocusableItemRemove:g}=p;return n.useEffect(()=>{if(o)return S(),()=>g()},[o,S,g]),(0,h.jsx)(y.ItemSlot,{scope:r,id:f,focusable:o,active:s,children:(0,h.jsx)(u.sG.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{o?p.onItemFocus(f):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>p.onItemFocus(f)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){p.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,r){var n;let i=(n=e.key,"rtl"!==r?n:"ArrowLeft"===n?"ArrowRight":"ArrowRight"===n?"ArrowLeft":n);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(i))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(i)))return k[i]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let r=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)r.reverse();else if("prev"===t||"next"===t){"prev"===t&&r.reverse();let n=r.indexOf(e.currentTarget);r=p.loop?function(e,t){return e.map((r,n)=>e[(t+n)%e.length])}(r,n+1):r.slice(n+1)}setTimeout(()=>j(r))}})})})});F.displayName=C;var k={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function j(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=document.activeElement;for(let n of e)if(n===r||(n.focus({preventScroll:t}),document.activeElement!==r))return}var z=r(8905),I="Tabs",[M,T]=(0,o.A)(I,[w]),E=w(),[N,D]=M(I),L=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,onValueChange:i,defaultValue:o,orientation:s="horizontal",dir:a,activationMode:c="automatic",...p}=e,m=(0,f.jH)(a),[v,y]=(0,d.i)({prop:n,onChange:i,defaultProp:o});return(0,h.jsx)(N,{scope:r,baseId:(0,l.B)(),value:v,onValueChange:y,orientation:s,dir:m,activationMode:c,children:(0,h.jsx)(u.sG.div,{dir:m,"data-orientation":s,...p,ref:t})})});L.displayName=I;var O="TabsList",P=n.forwardRef((e,t)=>{let{__scopeTabs:r,loop:n=!0,...i}=e,o=D(O,r),s=E(r);return(0,h.jsx)(A,{asChild:!0,...s,orientation:o.orientation,dir:o.dir,loop:n,children:(0,h.jsx)(u.sG.div,{role:"tablist","aria-orientation":o.orientation,...i,ref:t})})});P.displayName=O;var G="TabsTrigger",K=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:n,disabled:o=!1,...s}=e,a=D(G,r),l=E(r),c=H(a.baseId,n),d=U(a.baseId,n),f=n===a.value;return(0,h.jsx)(F,{asChild:!0,...l,focusable:!o,active:f,children:(0,h.jsx)(u.sG.button,{type:"button",role:"tab","aria-selected":f,"aria-controls":d,"data-state":f?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:c,...s,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():a.onValueChange(n)}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&a.onValueChange(n)}),onFocus:(0,i.m)(e.onFocus,()=>{let e="manual"!==a.activationMode;f||o||!e||a.onValueChange(n)})})})});K.displayName=G;var q="TabsContent",B=n.forwardRef((e,t)=>{let{__scopeTabs:r,value:i,forceMount:o,children:s,...a}=e,l=D(q,r),c=H(l.baseId,i),d=U(l.baseId,i),f=i===l.value,p=n.useRef(f);return n.useEffect(()=>{let e=requestAnimationFrame(()=>p.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,h.jsx)(z.C,{present:o||f,children:r=>{let{present:n}=r;return(0,h.jsx)(u.sG.div,{"data-state":f?"active":"inactive","data-orientation":l.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:d,tabIndex:0,...a,ref:t,style:{...e.style,animationDuration:p.current?"0s":void 0},children:n&&s})}})});function H(e,t){return"".concat(e,"-trigger-").concat(t)}function U(e,t){return"".concat(e,"-content-").concat(t)}B.displayName=q;var V=L,X=P,Z=K,$=B},1285:(e,t,r)=>{"use strict";r.d(t,{B:()=>l});var n,i=r(2115),o=r(2712),s=(n||(n=r.t(i,2)))["useId".toString()]||(()=>void 0),a=0;function l(e){let[t,r]=i.useState(s());return(0,o.N)(()=>{e||r(e=>e??String(a++))},[e]),e||(t?`radix-${t}`:"")}},1436:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Diamond",[["path",{d:"M2.7 10.3a2.41 2.41 0 0 0 0 3.41l7.59 7.59a2.41 2.41 0 0 0 3.41 0l7.59-7.59a2.41 2.41 0 0 0 0-3.41l-7.59-7.59a2.41 2.41 0 0 0-3.41 0Z",key:"1f1r0c"}]])},1539:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1586:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},2269:(e,t,r)=>{"use strict";var n=r(9509);r(8375);var i=r(2115),o=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(i),s=void 0!==n&&n.env&&!0,a=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,r=t.name,n=void 0===r?"stylesheet":r,i=t.optimizeForSpeed,o=void 0===i?s:i;u(a(n),"`name` must be a string"),this._name=n,this._deletedRulePlaceholder="#"+n+"-deleted-rule____{}",u("boolean"==typeof o,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=o,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t=e.prototype;return t.setOptimizeForSpeed=function(e){u("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),u(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},t.isOptimizeForSpeed=function(){return this._optimizeForSpeed},t.inject=function(){var e=this;if(u(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(s||console.warn("StyleSheet: optimizeForSpeed mode not supported falling back to standard mode."),this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,r){return"number"==typeof r?e._serverSheet.cssRules[r]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),r},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},t.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},t.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},t.insertRule=function(e,t){if(u(a(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var r=this.getSheet();"number"!=typeof t&&(t=r.cssRules.length);try{r.insertRule(e,t)}catch(t){return s||console.warn("StyleSheet: illegal rule: \n\n"+e+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),-1}}else{var n=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,n))}return this._rulesCount++},t.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var r="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!r.cssRules[e])return e;r.deleteRule(e);try{r.insertRule(t,e)}catch(n){s||console.warn("StyleSheet: illegal rule: \n\n"+t+"\n\nSee https://stackoverflow.com/q/20007992 for more info"),r.insertRule(this._deletedRulePlaceholder,e)}}else{var n=this._tags[e];u(n,"old rule at index `"+e+"` not found"),n.textContent=t}return e},t.deleteRule=function(e){if("undefined"==typeof window){this._serverSheet.deleteRule(e);return}if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];u(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},t.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},t.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,r){return r?t=t.concat(Array.prototype.map.call(e.getSheetForTag(r).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},t.makeStyleTag=function(e,t,r){t&&u(a(t),"makeStyleTag accepts only strings as second parameter");var n=document.createElement("style");this._nonce&&n.setAttribute("nonce",this._nonce),n.type="text/css",n.setAttribute("data-"+e,""),t&&n.appendChild(document.createTextNode(t));var i=document.head||document.getElementsByTagName("head")[0];return r?i.insertBefore(n,r):i.appendChild(n),n},function(e,t){for(var r=0;r<t.length;r++){var n=t[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(e,n.key,n)}}(e.prototype,[{key:"length",get:function(){return this._rulesCount}}]),e}();function u(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var c=function(e){for(var t=5381,r=e.length;r;)t=33*t^e.charCodeAt(--r);return t>>>0},d={};function f(e,t){if(!t)return"jsx-"+e;var r=String(t),n=e+r;return d[n]||(d[n]="jsx-"+c(e+"-"+r)),d[n]}function h(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var r=e+t;return d[r]||(d[r]=t.replace(/__jsx-style-dynamic-selector/g,e)),d[r]}var p=function(){function e(e){var t=void 0===e?{}:e,r=t.styleSheet,n=void 0===r?null:r,i=t.optimizeForSpeed,o=void 0!==i&&i;this._sheet=n||new l({name:"styled-jsx",optimizeForSpeed:o}),this._sheet.inject(),n&&"boolean"==typeof o&&(this._sheet.setOptimizeForSpeed(o),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var r=this.getIdAndRules(e),n=r.styleId,i=r.rules;if(n in this._instancesCounts){this._instancesCounts[n]+=1;return}var o=i.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[n]=o,this._instancesCounts[n]=1},t.remove=function(e){var t=this,r=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(r in this._instancesCounts,"styleId: `"+r+"` not found"),this._instancesCounts[r]-=1,this._instancesCounts[r]<1){var n=this._fromServer&&this._fromServer[r];n?(n.parentNode.removeChild(n),delete this._fromServer[r]):(this._indices[r].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[r]),delete this._instancesCounts[r]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],r=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return r[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,r;return t=this.cssRules(),void 0===(r=e)&&(r={}),t.map(function(e){var t=e[0],n=e[1];return o.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:r.nonce?r.nonce:void 0,dangerouslySetInnerHTML:{__html:n}})})},t.getIdAndRules=function(e){var t=e.children,r=e.dynamic,n=e.id;if(r){var i=f(n,r);return{styleId:i,rules:Array.isArray(t)?t.map(function(e){return h(i,e)}):[h(i,t)]}}return{styleId:f(n),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),m=i.createContext(null);m.displayName="StyleSheetContext";var v=o.default.useInsertionEffect||o.default.useLayoutEffect,y="undefined"!=typeof window?new p:void 0;function _(e){var t=y||i.useContext(m);return t&&("undefined"==typeof window?t.add(e):v(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}_.dynamic=function(e){return e.map(function(e){return f(e[0],e[1])}).join(" ")},t.style=_},2284:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(2115),i=r(6081),o=r(6101),s=r(9708),a=r(5155);function l(e){let t=e+"CollectionProvider",[r,l]=(0,i.A)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),d=e=>{let{scope:t,children:r}=e,i=n.useRef(null),o=n.useRef(new Map).current;return(0,a.jsx)(u,{scope:t,itemMap:o,collectionRef:i,children:r})};d.displayName=t;let f=e+"CollectionSlot",h=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,i=c(f,r),l=(0,o.s)(t,i.collectionRef);return(0,a.jsx)(s.DX,{ref:l,children:n})});h.displayName=f;let p=e+"CollectionItemSlot",m="data-radix-collection-item",v=n.forwardRef((e,t)=>{let{scope:r,children:i,...l}=e,u=n.useRef(null),d=(0,o.s)(t,u),f=c(p,r);return n.useEffect(()=>(f.itemMap.set(u,{ref:u,...l}),()=>void f.itemMap.delete(u))),(0,a.jsx)(s.DX,{[m]:"",ref:d,children:i})});return v.displayName=p,[{Provider:d,Slot:h,ItemSlot:v},function(t){let r=c(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}},3311:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},4186:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4315:(e,t,r)=>{"use strict";r.d(t,{jH:()=>o});var n=r(2115);r(5155);var i=n.createContext(void 0);function o(e){let t=n.useContext(i);return e||t||"ltr"}},7550:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},7580:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7951:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Crown",[["path",{d:"M11.562 3.266a.5.5 0 0 1 .876 0L15.39 8.87a1 1 0 0 0 1.516.294L21.183 5.5a.5.5 0 0 1 .798.519l-2.834 10.246a1 1 0 0 1-.956.734H5.81a1 1 0 0 1-.957-.734L2.02 6.02a.5.5 0 0 1 .798-.519l4.276 3.664a1 1 0 0 0 1.516-.294z",key:"1vdc57"}],["path",{d:"M5 21h14",key:"11awu3"}]])},8375:()=>{},8564:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9037:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("Award",[["path",{d:"m15.477 12.89 1.515 8.526a.5.5 0 0 1-.81.47l-3.58-2.687a1 1 0 0 0-1.197 0l-3.586 2.686a.5.5 0 0 1-.81-.469l1.514-8.526",key:"1yiouv"}],["circle",{cx:"12",cy:"8",r:"6",key:"1vp47v"}]])},9137:(e,t,r)=>{"use strict";e.exports=r(2269).style}}]);