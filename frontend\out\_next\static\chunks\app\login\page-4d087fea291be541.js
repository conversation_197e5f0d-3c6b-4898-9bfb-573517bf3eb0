(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{1106:(e,a,t)=>{Promise.resolve().then(t.bind(t,8485))},2486:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("Send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]])},2919:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("Lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]])},3695:(e,a,t)=>{"use strict";t.d(a,{S:()=>A});var s=t(5155),r=t(2115),l=t(6101),n=t(6081),o=t(5185),i=t(5845),d=t(5503),c=t(1275),m=t(8905),x=t(3655),u="Checkbox",[f,b]=(0,n.A)(u),[p,h]=f(u),j=r.forwardRef((e,a)=>{let{__scopeCheckbox:t,name:n,checked:d,defaultChecked:c,required:m,disabled:u,value:f="on",onCheckedChange:b,form:h,...j}=e,[y,g]=r.useState(null),k=(0,l.s)(a,e=>g(e)),C=r.useRef(!1),A=!y||h||!!y.closest("form"),[S=!1,P]=(0,i.i)({prop:d,defaultProp:c,onChange:b}),z=r.useRef(S);return r.useEffect(()=>{let e=null==y?void 0:y.form;if(e){let a=()=>P(z.current);return e.addEventListener("reset",a),()=>e.removeEventListener("reset",a)}},[y,P]),(0,s.jsxs)(p,{scope:t,state:S,disabled:u,children:[(0,s.jsx)(x.sG.button,{type:"button",role:"checkbox","aria-checked":v(S)?"mixed":S,"aria-required":m,"data-state":N(S),"data-disabled":u?"":void 0,disabled:u,value:f,...j,ref:k,onKeyDown:(0,o.m)(e.onKeyDown,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,o.m)(e.onClick,e=>{P(e=>!!v(e)||!e),A&&(C.current=e.isPropagationStopped(),C.current||e.stopPropagation())})}),A&&(0,s.jsx)(w,{control:y,bubbles:!C.current,name:n,value:f,checked:S,required:m,disabled:u,form:h,style:{transform:"translateX(-100%)"},defaultChecked:!v(c)&&c})]})});j.displayName=u;var y="CheckboxIndicator",g=r.forwardRef((e,a)=>{let{__scopeCheckbox:t,forceMount:r,...l}=e,n=h(y,t);return(0,s.jsx)(m.C,{present:r||v(n.state)||!0===n.state,children:(0,s.jsx)(x.sG.span,{"data-state":N(n.state),"data-disabled":n.disabled?"":void 0,...l,ref:a,style:{pointerEvents:"none",...e.style}})})});g.displayName=y;var w=e=>{let{control:a,checked:t,bubbles:l=!0,defaultChecked:n,...o}=e,i=r.useRef(null),m=(0,d.Z)(t),x=(0,c.X)(a);r.useEffect(()=>{let e=i.current,a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set;if(m!==t&&a){let s=new Event("click",{bubbles:l});e.indeterminate=v(t),a.call(e,!v(t)&&t),e.dispatchEvent(s)}},[m,t,l]);let u=r.useRef(!v(t)&&t);return(0,s.jsx)("input",{type:"checkbox","aria-hidden":!0,defaultChecked:null!=n?n:u.current,...o,tabIndex:-1,ref:i,style:{...e.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0}})};function v(e){return"indeterminate"===e}function N(e){return v(e)?"indeterminate":e?"checked":"unchecked"}var k=t(5196),C=t(3999);let A=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(j,{ref:a,className:(0,C.cn)("peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground",t),...r,children:(0,s.jsx)(g,{className:(0,C.cn)("flex items-center justify-center text-current"),children:(0,s.jsx)(k.A,{className:"h-4 w-4"})})})});A.displayName=j.displayName},5339:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},8485:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>P});var s=t(5155),r=t(9137),l=t.n(r),n=t(2115),o=t(7168),i=t(8482),d=t(9840),c=t(9852),m=t(3695),x=t(9588),u=t(5339),f=t(8883),b=t(2919),p=t(8749),h=t(2657);let j=(0,t(9946).A)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]);var y=t(646),g=t(2486),w=t(6194),v=t(3655),N=n.forwardRef((e,a)=>(0,s.jsx)(v.sG.label,{...e,ref:a,onMouseDown:a=>{var t;a.target.closest("button, input, select, textarea")||(null===(t=e.onMouseDown)||void 0===t||t.call(e,a),!a.defaultPrevented&&a.detail>1&&a.preventDefault())}}));N.displayName="Label";var k=t(2085),C=t(3999);let A=(0,k.F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),S=n.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(N,{ref:a,className:(0,C.cn)(A(),t),...r})});function P(){let[e,a]=(0,n.useState)({username:"",password:"",rememberMe:!1}),[t,r]=(0,n.useState)(!1),[v,N]=(0,n.useState)(!1),[k,C]=(0,n.useState)({}),[A,P]=(0,n.useState)(""),[z,E]=(0,n.useState)(!1),[R,M]=(0,n.useState)(!1),[D,L]=(0,n.useState)("email"),[F,O]=(0,n.useState)({email:"",code:"",newPassword:"",confirmPassword:""}),[I,$]=(0,n.useState)(!1),[_,G]=(0,n.useState)(!1),[q,J]=(0,n.useState)(""),[Z,T]=(0,n.useState)(!1),[V,H]=(0,n.useState)(!1),[K,U]=(0,n.useState)(0),[X,Y]=(0,n.useState)(!1);(0,n.useEffect)(()=>{Y(!0)},[]),(0,n.useEffect)(()=>{E(!0);let e=localStorage.getItem("rememberedCredentials");if(e){let{username:t,rememberMe:s}=JSON.parse(e);a(e=>({...e,username:t,rememberMe:s}))}},[]),(0,n.useEffect)(()=>{if(K>0){let e=setTimeout(()=>U(K-1),1e3);return()=>clearTimeout(e)}},[K]);let B=e=>/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e),W=e=>e.length>=2,Q=()=>{let a={};return e.username.trim()||(a.username="用户名或邮箱不能为空"),e.password.trim()?W(e.password)||(a.password="密码长度至少为2位"):a.password="密码不能为空",C(a),0===Object.keys(a).length},ee=(e,t)=>{a(a=>({...a,[e]:t})),k[e]&&C(a=>({...a,[e]:""})),A&&P("")},ea=async a=>{if(a.preventDefault(),Q()){N(!0),P("");try{await w.j2.login({username:e.username,password:e.password}),e.rememberMe?localStorage.setItem("rememberedCredentials",JSON.stringify({username:e.username,rememberMe:!0})):localStorage.removeItem("rememberedCredentials"),window.location.href="/"}catch(e){P(e instanceof Error?e.message:"登录失败，请检查网络连接后重试")}finally{N(!1)}}},et=async()=>{if(J(""),!F.email){J("请输入邮箱地址");return}if(!B(F.email)){J("请输入有效的邮箱地址");return}T(!0);try{await w.j2.forgotPassword({email:F.email}),L("verify"),U(60)}catch(e){console.error("Send reset code error:",e),J(e.message||"发送验证码失败，请重试")}finally{T(!1)}},es=async()=>{if(J(""),!F.code||!F.newPassword||!F.confirmPassword){J("请填写所有字段");return}if(F.newPassword!==F.confirmPassword){J("新密码和确认密码不匹配");return}if(F.newPassword.length<6){J("新密码长度不能少于6位");return}H(!0);try{await w.j2.resetPassword({email:F.email,code:F.code,newPassword:F.newPassword}),L("success")}catch(e){console.error("Reset password error:",e),J(e.message||"重置密码失败，请重试")}finally{H(!1)}},er=()=>{M(!1),L("email"),O({email:"",code:"",newPassword:"",confirmPassword:""}),J(""),U(0)},el=[{left:20,top:25,duration:11},{left:75,top:40,duration:9},{left:45,top:65,duration:12},{left:85,top:20,duration:10},{left:30,top:80,duration:13},{left:65,top:50,duration:8},{left:55,top:35,duration:11},{left:35,top:85,duration:9}];return(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-purple-50/20 flex items-center justify-center p-6 relative overflow-hidden",children:[X&&(0,s.jsx)(()=>(0,s.jsx)("div",{className:"absolute inset-0 overflow-hidden pointer-events-none",children:el.map((e,a)=>(0,s.jsx)("div",{className:"absolute w-2 h-2 bg-gradient-to-r from-blue-400 to-purple-400 rounded-full opacity-20 animate-float",style:{left:"".concat(e.left,"%"),top:"".concat(e.top,"%"),animationDelay:"".concat(2*a,"s"),animationDuration:"".concat(e.duration,"s")}},a))}),{className:"jsx-3e1eea4381e0e46b"}),(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute top-20 right-20 w-72 h-72 bg-gradient-to-r from-blue-200/20 to-purple-200/20 rounded-full blur-3xl animate-pulse"}),(0,s.jsx)("div",{style:{animationDelay:"2s"},className:"jsx-3e1eea4381e0e46b absolute bottom-20 left-20 w-96 h-96 bg-gradient-to-r from-purple-200/20 to-pink-200/20 rounded-full blur-3xl animate-pulse"}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b "+"w-full max-w-md transition-all duration-1000 ".concat(z?"opacity-100 translate-y-0":"opacity-0 translate-y-8"),children:[(0,s.jsxs)(i.Zp,{className:"border-0 shadow-2xl bg-white/90 backdrop-blur-xl relative overflow-hidden",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-blue-500/5 to-purple-500/5"}),(0,s.jsxs)(i.aR,{className:"text-center pb-8 relative",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b flex justify-center mb-6",children:(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl blur-lg opacity-50 animate-pulse"}),(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b relative p-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl shadow-xl",children:(0,s.jsx)(x.A,{className:"w-8 h-8 text-white"})})]})}),(0,s.jsx)(i.ZB,{className:"text-3xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 bg-clip-text text-transparent mb-2",children:"欢迎回来"}),(0,s.jsx)("p",{className:"jsx-3e1eea4381e0e46b text-gray-600 text-lg",children:"登录您的 AI 语音工作室账户"})]}),(0,s.jsxs)(i.Wu,{className:"relative",children:[(0,s.jsxs)("form",{onSubmit:ea,className:"jsx-3e1eea4381e0e46b space-y-6",children:[A&&(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b p-4 bg-red-50 border border-red-200 rounded-lg flex items-center gap-3 animate-fade-in",children:[(0,s.jsx)(u.A,{className:"w-5 h-5 text-red-500 flex-shrink-0"}),(0,s.jsx)("p",{className:"jsx-3e1eea4381e0e46b text-red-700 text-sm",children:A})]}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-4",children:[(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b",children:[(0,s.jsx)(S,{htmlFor:"username",className:"text-sm font-medium text-gray-700 group-hover:text-indigo-600 transition-colors duration-300",children:"用户名或邮箱"}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b mt-1 relative rounded-2xl shadow-sm",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b pointer-events-none absolute inset-y-0 left-0 flex items-center pl-4",children:(0,s.jsx)(f.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)(c.p,{id:"username",name:"username",type:"text",autoComplete:"username",required:!0,placeholder:"请输入您的用户名或邮箱",value:e.username,onChange:e=>ee("username",e.target.value),className:"pl-12 ".concat(k.username?"border-red-500 ring-red-500":"border-gray-300 focus:border-indigo-500 focus:ring-indigo-500")})]}),k.username&&(0,s.jsx)("p",{className:"jsx-3e1eea4381e0e46b mt-2 text-sm text-red-600",children:k.username})]}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,s.jsx)("label",{htmlFor:"password",className:"jsx-3e1eea4381e0e46b block text-sm font-semibold text-gray-700",children:"密码"}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)(c.p,{id:"password",type:t?"text":"password",value:e.password,onChange:e=>ee("password",e.target.value),placeholder:"请输入您的密码",className:"pl-10 pr-12 h-12 text-lg border-2 transition-all duration-300 ".concat(k.password?"border-red-400 focus:border-red-500 focus:ring-red-100":"border-gray-200 focus:border-blue-400 focus:ring-blue-100"),disabled:v}),(0,s.jsx)("button",{type:"button",onClick:()=>r(!t),disabled:v,className:"jsx-3e1eea4381e0e46b absolute inset-y-0 right-0 pr-3 flex items-center hover:bg-gray-50 rounded-r-lg transition-colors duration-200",children:t?(0,s.jsx)(p.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,s.jsx)(h.A,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]}),k.password&&(0,s.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-red-500 text-sm flex items-center gap-2 animate-fade-in",children:[(0,s.jsx)(u.A,{className:"w-4 h-4"}),k.password]})]})]}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center space-x-2",children:[(0,s.jsx)(m.S,{id:"rememberMe",checked:e.rememberMe,onCheckedChange:e=>ee("rememberMe",e),disabled:v,className:"data-[state=checked]:bg-blue-500 data-[state=checked]:border-blue-500"}),(0,s.jsx)("label",{htmlFor:"rememberMe",className:"jsx-3e1eea4381e0e46b text-sm text-gray-700 cursor-pointer select-none",children:"记住我"})]}),(0,s.jsx)("button",{type:"button",onClick:()=>{M(!0),L("email"),J("")},disabled:v,className:"jsx-3e1eea4381e0e46b text-sm text-blue-600 hover:text-blue-800 hover:underline transition-colors duration-200",children:"忘记密码？"})]}),(0,s.jsxs)(o.$,{type:"submit",disabled:v,className:"w-full h-12 text-lg font-bold bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 disabled:from-gray-400 disabled:to-gray-500 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-105 disabled:scale-100 relative overflow-hidden group",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-white/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"}),(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b relative z-10 flex items-center justify-center gap-3",children:v?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin"}),"登录中..."]}):(0,s.jsxs)(s.Fragment,{children:["登录",(0,s.jsx)(j,{className:"w-5 h-5"})]})})]})]}),(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b mt-8 text-center",children:(0,s.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-gray-600",children:["还没有账户？"," ",(0,s.jsx)("button",{onClick:()=>window.location.href="/register",className:"jsx-3e1eea4381e0e46b text-blue-600 hover:text-blue-800 font-semibold hover:underline transition-colors duration-200",children:"立即注册"})]})})]})]}),(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b mt-6 text-center",children:(0,s.jsx)("button",{onClick:()=>window.location.href="/",className:"jsx-3e1eea4381e0e46b text-gray-600 hover:text-gray-800 text-sm hover:underline transition-colors duration-200",children:"← 返回首页"})})]}),(0,s.jsx)(d.lG,{open:R,onOpenChange:M,children:(0,s.jsxs)(d.Cf,{className:"sm:max-w-md bg-white/95 backdrop-blur-xl border border-gray-200",children:[(0,s.jsxs)(d.c7,{children:[(0,s.jsxs)(d.L3,{className:"flex items-center gap-2 text-xl font-bold bg-gradient-to-r from-gray-900 to-gray-700 bg-clip-text text-transparent",children:[(0,s.jsx)(f.A,{className:"w-5 h-5 text-blue-600"}),"email"===D&&"重置密码","verify"===D&&"验证邮箱","success"===D&&"重置成功"]}),(0,s.jsxs)(d.rr,{className:"text-gray-600",children:["email"===D&&"请输入您的邮箱地址，我们将发送验证码","verify"===D&&"请输入验证码和新密码","success"===D&&"密码重置成功，请使用新密码登录"]})]}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-4 py-4",children:["email"===D&&(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,s.jsx)("label",{className:"jsx-3e1eea4381e0e46b text-sm font-medium text-gray-700",children:"邮箱地址"}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(f.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)(c.p,{type:"email",value:F.email,onChange:e=>O(a=>({...a,email:e.target.value})),placeholder:"请输入您的邮箱地址",className:"pl-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"})]})]}),"verify"===D&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,s.jsx)("label",{className:"jsx-3e1eea4381e0e46b text-sm font-medium text-gray-700",children:"验证码"}),(0,s.jsx)(c.p,{type:"text",value:F.code,onChange:e=>O(a=>({...a,code:e.target.value})),placeholder:"请输入6位验证码",className:"border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50",maxLength:6}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex justify-between items-center",children:[(0,s.jsxs)("p",{className:"jsx-3e1eea4381e0e46b text-sm text-gray-500",children:["验证码已发送至 ",F.email]}),(0,s.jsx)("button",{type:"button",onClick:et,disabled:K>0||Z,className:"jsx-3e1eea4381e0e46b text-sm text-blue-600 hover:text-blue-800 disabled:text-gray-400 disabled:cursor-not-allowed",children:K>0?"".concat(K,"s后重发"):"重新发送"})]})]}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,s.jsx)("label",{className:"jsx-3e1eea4381e0e46b text-sm font-medium text-gray-700",children:"新密码"}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)(c.p,{type:I?"text":"password",value:F.newPassword,onChange:e=>O(a=>({...a,newPassword:e.target.value})),placeholder:"请输入新密码（至少6位）",className:"pl-10 pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"}),(0,s.jsx)("button",{type:"button",onClick:()=>$(!I),className:"jsx-3e1eea4381e0e46b absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:I?(0,s.jsx)(p.A,{className:"w-4 h-4"}):(0,s.jsx)(h.A,{className:"w-4 h-4"})})]})]}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b space-y-2",children:[(0,s.jsx)("label",{className:"jsx-3e1eea4381e0e46b text-sm font-medium text-gray-700",children:"确认新密码"}),(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(b.A,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)(c.p,{type:_?"text":"password",value:F.confirmPassword,onChange:e=>O(a=>({...a,confirmPassword:e.target.value})),placeholder:"请再次输入新密码",className:"pl-10 pr-10 border-2 transition-all duration-300 focus:border-blue-400 focus:ring-4 focus:ring-blue-50"}),(0,s.jsx)("button",{type:"button",onClick:()=>G(!_),className:"jsx-3e1eea4381e0e46b absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors",children:_?(0,s.jsx)(p.A,{className:"w-4 h-4"}):(0,s.jsx)(h.A,{className:"w-4 h-4"})})]})]})]}),"success"===D&&(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b text-center py-6",children:[(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b relative mx-auto w-16 h-16 mb-4",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b absolute inset-0 bg-gradient-to-r from-green-400 to-emerald-400 rounded-full blur-md opacity-50"}),(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b relative p-4 bg-gradient-to-r from-green-500 to-emerald-500 rounded-full shadow-lg",children:(0,s.jsx)(y.A,{className:"w-8 h-8 text-white"})})]}),(0,s.jsx)("p",{className:"jsx-3e1eea4381e0e46b text-gray-600 mb-2",children:"密码重置成功！"}),(0,s.jsx)("p",{className:"jsx-3e1eea4381e0e46b text-sm text-gray-500",children:"请使用新密码登录您的账户"})]}),q&&(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center gap-2 text-red-700",children:[(0,s.jsx)(u.A,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"jsx-3e1eea4381e0e46b text-sm font-medium",children:q})]})})]}),(0,s.jsxs)(d.Es,{className:"flex gap-2",children:[(0,s.jsx)(o.$,{variant:"outline",onClick:er,disabled:Z||V,className:"flex-1",children:"success"===D?"关闭":"取消"}),"email"===D&&(0,s.jsx)(o.$,{onClick:et,disabled:Z,className:"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white",children:Z?(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center gap-2",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),"发送中..."]}):(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center gap-2",children:[(0,s.jsx)(g.A,{className:"w-4 h-4"}),"发送验证码"]})}),"verify"===D&&(0,s.jsx)(o.$,{onClick:es,disabled:V,className:"flex-1 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 text-white",children:V?(0,s.jsxs)("div",{className:"jsx-3e1eea4381e0e46b flex items-center gap-2",children:[(0,s.jsx)("div",{className:"jsx-3e1eea4381e0e46b w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin"}),"重置中..."]}):"确认重置"}),"success"===D&&(0,s.jsx)(o.$,{onClick:er,className:"flex-1 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white",children:"返回登录"})]})]})}),(0,s.jsx)(l(),{id:"3e1eea4381e0e46b",children:"@-webkit-keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-moz-keyframes float{0%,100%{-moz-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-moz-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-o-keyframes float{0%,100%{-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@keyframes float{0%,100%{-webkit-transform:translatey(0px)rotate(0deg);-moz-transform:translatey(0px)rotate(0deg);-o-transform:translatey(0px)rotate(0deg);transform:translatey(0px)rotate(0deg)}50%{-webkit-transform:translatey(-20px)rotate(180deg);-moz-transform:translatey(-20px)rotate(180deg);-o-transform:translatey(-20px)rotate(180deg);transform:translatey(-20px)rotate(180deg)}}@-webkit-keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);transform:translatey(0)}}@-moz-keyframes fade-in{from{opacity:0;-moz-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-moz-transform:translatey(0);transform:translatey(0)}}@-o-keyframes fade-in{from{opacity:0;-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-o-transform:translatey(0);transform:translatey(0)}}@keyframes fade-in{from{opacity:0;-webkit-transform:translatey(10px);-moz-transform:translatey(10px);-o-transform:translatey(10px);transform:translatey(10px)}to{opacity:1;-webkit-transform:translatey(0);-moz-transform:translatey(0);-o-transform:translatey(0);transform:translatey(0)}}.animate-float.jsx-3e1eea4381e0e46b{-webkit-animation:float 8s ease-in-out infinite;-moz-animation:float 8s ease-in-out infinite;-o-animation:float 8s ease-in-out infinite;animation:float 8s ease-in-out infinite}.animate-fade-in.jsx-3e1eea4381e0e46b{-webkit-animation:fade-in.3s ease-out forwards;-moz-animation:fade-in.3s ease-out forwards;-o-animation:fade-in.3s ease-out forwards;animation:fade-in.3s ease-out forwards}.animate-float.jsx-3e1eea4381e0e46b{will-change:transform;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.animate-fade-in.jsx-3e1eea4381e0e46b{will-change:transform,opacity;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}"})]})}S.displayName=N.displayName},8883:(e,a,t)=>{"use strict";t.d(a,{A:()=>s});let s=(0,t(9946).A)("Mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]])},9840:(e,a,t)=>{"use strict";t.d(a,{Cf:()=>m,Es:()=>u,L3:()=>f,c7:()=>x,lG:()=>i,rr:()=>b});var s=t(5155),r=t(2115),l=t(3651),n=t(4416),o=t(3999);let i=l.bL;l.l9;let d=l.ZL;l.bm;let c=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.hJ,{ref:a,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});c.displayName=l.hJ.displayName;let m=r.forwardRef((e,a)=>{let{className:t,children:r,...i}=e;return(0,s.jsxs)(d,{children:[(0,s.jsx)(c,{}),(0,s.jsxs)(l.UC,{ref:a,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...i,children:[r,(0,s.jsxs)(l.bm,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(n.A,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});m.displayName=l.UC.displayName;let x=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",a),...t})};x.displayName="DialogHeader";let u=e=>{let{className:a,...t}=e;return(0,s.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...t})};u.displayName="DialogFooter";let f=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.hE,{ref:a,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})});f.displayName=l.hE.displayName;let b=r.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(l.VY,{ref:a,className:(0,o.cn)("text-sm text-muted-foreground",t),...r})});b.displayName=l.VY.displayName}},e=>{var a=a=>e(e.s=a);e.O(0,[228,550,758,927,441,684,358],()=>a(1106)),_N_E=e.O()}]);