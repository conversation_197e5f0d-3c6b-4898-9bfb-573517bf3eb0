"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[678],{381:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1007:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1243:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("TriangleAlert",[["path",{d:"m21.73 18-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3",key:"wmoenq"}],["path",{d:"M12 9v4",key:"juzpu7"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},1284:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Info",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 16v-4",key:"1dtifu"}],["path",{d:"M12 8h.01",key:"e9boi3"}]])},1482:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Filter",[["polygon",{points:"22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3",key:"1yg77f"}]])},1539:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]])},1586:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("CreditCard",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]])},1788:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},2178:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Pause",[["rect",{x:"14",y:"4",width:"4",height:"16",rx:"1",key:"zuxfzm"}],["rect",{x:"6",y:"4",width:"4",height:"16",rx:"1",key:"1okwgv"}]])},2284:(e,t,r)=>{r.d(t,{N:()=>d});var a=r(2115),n=r(6081),l=r(6101),o=r(9708),i=r(5155);function d(e){let t=e+"CollectionProvider",[r,d]=(0,n.A)(t),[u,c]=r(t,{collectionRef:{current:null},itemMap:new Map}),h=e=>{let{scope:t,children:r}=e,n=a.useRef(null),l=a.useRef(new Map).current;return(0,i.jsx)(u,{scope:t,itemMap:l,collectionRef:n,children:r})};h.displayName=t;let s=e+"CollectionSlot",y=a.forwardRef((e,t)=>{let{scope:r,children:a}=e,n=c(s,r),d=(0,l.s)(t,n.collectionRef);return(0,i.jsx)(o.DX,{ref:d,children:a})});y.displayName=s;let p=e+"CollectionItemSlot",f="data-radix-collection-item",k=a.forwardRef((e,t)=>{let{scope:r,children:n,...d}=e,u=a.useRef(null),h=(0,l.s)(t,u),s=c(p,r);return a.useEffect(()=>(s.itemMap.set(u,{ref:u,...d}),()=>void s.itemMap.delete(u))),(0,i.jsx)(o.DX,{[f]:"",ref:h,children:n})});return k.displayName=p,[{Provider:h,Slot:y,ItemSlot:k},function(t){let r=c(e+"CollectionConsumer",t);return a.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(f,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},d]}},2713:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("ChartColumn",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3311:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},3314:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Cpu",[["rect",{width:"16",height:"16",x:"4",y:"4",rx:"2",key:"14l7u7"}],["rect",{width:"6",height:"6",x:"9",y:"9",rx:"1",key:"5aljv4"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]])},3904:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},4186:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},4315:(e,t,r)=>{r.d(t,{jH:()=>l});var a=r(2115);r(5155);var n=a.createContext(void 0);function l(e){let t=a.useContext(n);return e||t||"ltr"}},4357:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},4788:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("CircleHelp",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3",key:"1u773s"}],["path",{d:"M12 17h.01",key:"p32p05"}]])},4835:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},4869:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},5273:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Volume2",[["path",{d:"M11 4.702a.705.705 0 0 0-1.203-.498L6.413 7.587A1.4 1.4 0 0 1 5.416 8H3a1 1 0 0 0-1 1v6a1 1 0 0 0 1 1h2.416a1.4 1.4 0 0 1 .997.413l3.383 3.384A.705.705 0 0 0 11 19.298z",key:"uqj9uw"}],["path",{d:"M16 9a5 5 0 0 1 0 6",key:"1q6k2b"}],["path",{d:"M19.364 18.364a9 9 0 0 0 0-12.728",key:"ijwkga"}]])},5313:(e,t,r)=>{r.d(t,{Q6:()=>G,bL:()=>O,zi:()=>T,CC:()=>X});var a=r(2115);function n(e,[t,r]){return Math.min(r,Math.max(t,e))}var l=r(5185),o=r(6101),i=r(6081),d=r(5845),u=r(4315),c=r(5503),h=r(1275),s=r(3655),y=r(2284),p=r(5155),f=["PageUp","PageDown"],k=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],m={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},v="Slider",[A,M,x]=(0,y.N)(v),[g,w]=(0,i.A)(v,[x]),[b,S]=g(v),j=a.forwardRef((e,t)=>{let{name:r,min:o=0,max:i=100,step:u=1,orientation:c="horizontal",disabled:h=!1,minStepsBetweenThumbs:s=0,defaultValue:y=[o],value:m,onValueChange:v=()=>{},onValueCommit:M=()=>{},inverted:x=!1,form:g,...w}=e,S=a.useRef(new Set),j=a.useRef(0),R="horizontal"===c,[z=[],P]=(0,d.i)({prop:m,defaultProp:y,onChange:e=>{var t;null===(t=[...S.current][j.current])||void 0===t||t.focus(),v(e)}}),E=a.useRef(z);function q(e,t){let{commit:r}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{commit:!1},a=(String(u).split(".")[1]||"").length,l=n(function(e,t){let r=Math.pow(10,t);return Math.round(e*r)/r}(Math.round((e-o)/u)*u+o,a),[o,i]);P(function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],a=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1?arguments[1]:void 0,r=arguments.length>2?arguments[2]:void 0,a=[...e];return a[r]=t,a.sort((e,t)=>e-t)}(e,l,t);if(!function(e,t){if(t>0)return Math.min(...e.slice(0,-1).map((t,r)=>e[r+1]-t))>=t;return!0}(a,s*u))return e;{j.current=a.indexOf(l);let t=String(a)!==String(e);return t&&r&&M(a),t?a:e}})}return(0,p.jsx)(b,{scope:e.__scopeSlider,name:r,disabled:h,min:o,max:i,valueIndexToChangeRef:j,thumbs:S.current,values:z,orientation:c,form:g,children:(0,p.jsx)(A.Provider,{scope:e.__scopeSlider,children:(0,p.jsx)(A.Slot,{scope:e.__scopeSlider,children:(0,p.jsx)(R?C:D,{"aria-disabled":h,"data-disabled":h?"":void 0,...w,ref:t,onPointerDown:(0,l.m)(w.onPointerDown,()=>{h||(E.current=z)}),min:o,max:i,inverted:x,onSlideStart:h?void 0:function(e){let t=function(e,t){if(1===e.length)return 0;let r=e.map(e=>Math.abs(e-t)),a=Math.min(...r);return r.indexOf(a)}(z,e);q(e,t)},onSlideMove:h?void 0:function(e){q(e,j.current)},onSlideEnd:h?void 0:function(){let e=E.current[j.current];z[j.current]!==e&&M(z)},onHomeKeyDown:()=>!h&&q(o,0,{commit:!0}),onEndKeyDown:()=>!h&&q(i,z.length-1,{commit:!0}),onStepKeyDown:e=>{let{event:t,direction:r}=e;if(!h){let e=f.includes(t.key)||t.shiftKey&&k.includes(t.key),a=j.current;q(z[a]+u*(e?10:1)*r,a,{commit:!0})}}})})})})});j.displayName=v;var[R,z]=g(v,{startEdge:"left",endEdge:"right",size:"width",direction:1}),C=a.forwardRef((e,t)=>{let{min:r,max:n,dir:l,inverted:i,onSlideStart:d,onSlideMove:c,onSlideEnd:h,onStepKeyDown:s,...y}=e,[f,k]=a.useState(null),v=(0,o.s)(t,e=>k(e)),A=a.useRef(void 0),M=(0,u.jH)(l),x="ltr"===M,g=x&&!i||!x&&i;function w(e){let t=A.current||f.getBoundingClientRect(),a=U([0,t.width],g?[r,n]:[n,r]);return A.current=t,a(e-t.left)}return(0,p.jsx)(R,{scope:e.__scopeSlider,startEdge:g?"left":"right",endEdge:g?"right":"left",direction:g?1:-1,size:"width",children:(0,p.jsx)(P,{dir:M,"data-orientation":"horizontal",...y,ref:v,style:{...y.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:e=>{let t=w(e.clientX);null==d||d(t)},onSlideMove:e=>{let t=w(e.clientX);null==c||c(t)},onSlideEnd:()=>{A.current=void 0,null==h||h()},onStepKeyDown:e=>{let t=m[g?"from-left":"from-right"].includes(e.key);null==s||s({event:e,direction:t?-1:1})}})})}),D=a.forwardRef((e,t)=>{let{min:r,max:n,inverted:l,onSlideStart:i,onSlideMove:d,onSlideEnd:u,onStepKeyDown:c,...h}=e,s=a.useRef(null),y=(0,o.s)(t,s),f=a.useRef(void 0),k=!l;function v(e){let t=f.current||s.current.getBoundingClientRect(),a=U([0,t.height],k?[n,r]:[r,n]);return f.current=t,a(e-t.top)}return(0,p.jsx)(R,{scope:e.__scopeSlider,startEdge:k?"bottom":"top",endEdge:k?"top":"bottom",size:"height",direction:k?1:-1,children:(0,p.jsx)(P,{"data-orientation":"vertical",...h,ref:y,style:{...h.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:e=>{let t=v(e.clientY);null==i||i(t)},onSlideMove:e=>{let t=v(e.clientY);null==d||d(t)},onSlideEnd:()=>{f.current=void 0,null==u||u()},onStepKeyDown:e=>{let t=m[k?"from-bottom":"from-top"].includes(e.key);null==c||c({event:e,direction:t?-1:1})}})})}),P=a.forwardRef((e,t)=>{let{__scopeSlider:r,onSlideStart:a,onSlideMove:n,onSlideEnd:o,onHomeKeyDown:i,onEndKeyDown:d,onStepKeyDown:u,...c}=e,h=S(v,r);return(0,p.jsx)(s.sG.span,{...c,ref:t,onKeyDown:(0,l.m)(e.onKeyDown,e=>{"Home"===e.key?(i(e),e.preventDefault()):"End"===e.key?(d(e),e.preventDefault()):f.concat(k).includes(e.key)&&(u(e),e.preventDefault())}),onPointerDown:(0,l.m)(e.onPointerDown,e=>{let t=e.target;t.setPointerCapture(e.pointerId),e.preventDefault(),h.thumbs.has(t)?t.focus():a(e)}),onPointerMove:(0,l.m)(e.onPointerMove,e=>{e.target.hasPointerCapture(e.pointerId)&&n(e)}),onPointerUp:(0,l.m)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&(t.releasePointerCapture(e.pointerId),o(e))})})}),E="SliderTrack",q=a.forwardRef((e,t)=>{let{__scopeSlider:r,...a}=e,n=S(E,r);return(0,p.jsx)(s.sG.span,{"data-disabled":n.disabled?"":void 0,"data-orientation":n.orientation,...a,ref:t})});q.displayName=E;var H="SliderRange",_=a.forwardRef((e,t)=>{let{__scopeSlider:r,...n}=e,l=S(H,r),i=z(H,r),d=a.useRef(null),u=(0,o.s)(t,d),c=l.values.length,h=l.values.map(e=>K(e,l.min,l.max)),y=c>1?Math.min(...h):0,f=100-Math.max(...h);return(0,p.jsx)(s.sG.span,{"data-orientation":l.orientation,"data-disabled":l.disabled?"":void 0,...n,ref:u,style:{...e.style,[i.startEdge]:y+"%",[i.endEdge]:f+"%"}})});_.displayName=H;var L="SliderThumb",I=a.forwardRef((e,t)=>{let r=M(e.__scopeSlider),[n,l]=a.useState(null),i=(0,o.s)(t,e=>l(e)),d=a.useMemo(()=>n?r().findIndex(e=>e.ref.current===n):-1,[r,n]);return(0,p.jsx)(N,{...e,ref:i,index:d})}),N=a.forwardRef((e,t)=>{let{__scopeSlider:r,index:n,name:i,...d}=e,u=S(L,r),c=z(L,r),[y,f]=a.useState(null),k=(0,o.s)(t,e=>f(e)),m=!y||u.form||!!y.closest("form"),v=(0,h.X)(y),M=u.values[n],x=void 0===M?0:K(M,u.min,u.max),g=function(e,t){return t>2?"Value ".concat(e+1," of ").concat(t):2===t?["Minimum","Maximum"][e]:void 0}(n,u.values.length),w=null==v?void 0:v[c.size],b=w?function(e,t,r){let a=e/2,n=U([0,50],[0,a]);return(a-n(t)*r)*r}(w,x,c.direction):0;return a.useEffect(()=>{if(y)return u.thumbs.add(y),()=>{u.thumbs.delete(y)}},[y,u.thumbs]),(0,p.jsxs)("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[c.startEdge]:"calc(".concat(x,"% + ").concat(b,"px)")},children:[(0,p.jsx)(A.ItemSlot,{scope:e.__scopeSlider,children:(0,p.jsx)(s.sG.span,{role:"slider","aria-label":e["aria-label"]||g,"aria-valuemin":u.min,"aria-valuenow":M,"aria-valuemax":u.max,"aria-orientation":u.orientation,"data-orientation":u.orientation,"data-disabled":u.disabled?"":void 0,tabIndex:u.disabled?void 0:0,...d,ref:k,style:void 0===M?{display:"none"}:e.style,onFocus:(0,l.m)(e.onFocus,()=>{u.valueIndexToChangeRef.current=n})})}),m&&(0,p.jsx)(V,{name:null!=i?i:u.name?u.name+(u.values.length>1?"[]":""):void 0,form:u.form,value:M},n)]})});I.displayName=L;var V=e=>{let{value:t,...r}=e,n=a.useRef(null),l=(0,c.Z)(t);return a.useEffect(()=>{let e=n.current,r=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"value").set;if(l!==t&&r){let a=new Event("input",{bubbles:!0});r.call(e,t),e.dispatchEvent(a)}},[l,t]),(0,p.jsx)("input",{style:{display:"none"},...r,ref:n,defaultValue:t})};function K(e,t,r){return n(100/(r-t)*(e-t),[0,100])}function U(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let a=(t[1]-t[0])/(e[1]-e[0]);return t[0]+a*(r-e[0])}}var O=j,X=q,G=_,T=I},5670:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("UserCheck",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["polyline",{points:"16 11 18 13 22 9",key:"1pwet4"}]])},5690:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Play",[["polygon",{points:"6 3 20 12 6 21 6 3",key:"1oa8hb"}]])},5968:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},6474:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},6517:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Wifi",[["path",{d:"M12 20h.01",key:"zekei9"}],["path",{d:"M2 8.82a15 15 0 0 1 20 0",key:"dnpr2z"}],["path",{d:"M5 12.859a10 10 0 0 1 14 0",key:"1x1e6c"}],["path",{d:"M8.5 16.429a5 5 0 0 1 7 0",key:"1bycff"}]])},7580:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]])},7924:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},9323:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("FileX",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"m14.5 12.5-5 5",key:"b62r18"}],["path",{d:"m9.5 12.5 5 5",key:"1rk7el"}]])},9803:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]])},9869:(e,t,r)=>{r.d(t,{A:()=>a});let a=(0,r(9946).A)("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]])}}]);