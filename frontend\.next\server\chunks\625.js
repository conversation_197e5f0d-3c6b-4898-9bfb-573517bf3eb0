exports.id=625,exports.ids=[625],exports.modules={113:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},609:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},1702:(e,t,r)=>{"use strict";r.d(t,{dj:()=>h});var s=r(3210);let a=0,o=new Map,i=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?i(r):e.toasts.forEach(e=>{i(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],d={toasts:[]};function u(e){d=n(d,e),c.forEach(e=>{e(d)})}function l({...e}){let t=(a=(a+1)%Number.MAX_SAFE_INTEGER).toString(),r=()=>u({type:"DISMISS_TOAST",toastId:t});return u({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||r()}}}),{id:t,dismiss:r,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function h(){let[e,t]=s.useState(d);return s.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:l,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},2358:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>s});let s=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\myaitts\\frontend\\components\\ui\\toaster.tsx","Toaster")},2704:()=>{},3688:(e,t,r)=>{Promise.resolve().then(r.bind(r,9794))},4426:(e,t,r)=>{"use strict";r.d(t,{j2:()=>c,lY:()=>u,uZ:()=>d});var s=r(9556);class a{static async login(e){try{let t=await s.uE.post(s.Sn.AUTH.LOGIN,e);return s.tC.setTokens(t.access_token,t.refresh_token,e.username),t}catch(e){throw console.error("Login error:",e),e}}static async register(e){try{let t=await s.uE.post(s.Sn.AUTH.REGISTER,e);return s.tC.setTokens(t.access_token,t.refresh_token,e.username),t}catch(e){throw console.error("Register error:",e),e}}static async sendVerificationCode(e){try{return await s.uE.post(s.Sn.AUTH.SEND_VERIFICATION,e)}catch(e){throw console.error("Send verification error:",e),e}}static async verifyEmailAndRegister(e){try{let t=await s.uE.post(s.Sn.AUTH.VERIFY_EMAIL,e);return s.tC.setTokens(t.access_token,t.refresh_token,e.email),t}catch(e){throw console.error("Verify email error:",e),e}}static async refreshToken(){try{let e=s.tC.getRefreshToken();if(!e)throw Error("No refresh token available");let t=await s.uE.post(s.Sn.AUTH.REFRESH,{refresh_token:e});return s.tC.setTokens(t.access_token,t.refresh_token),t}catch(e){throw console.error("Refresh token error:",e),s.tC.clearTokens(),e}}static async logout(){try{s.tC.clearTokens()}catch(e){console.error("Logout error:",e),s.tC.clearTokens()}}static async getUserQuota(){try{return await this.withTokenRefresh(async()=>await s.uE.get(s.Sn.USER.QUOTA,!0))}catch(e){throw console.error("Get user quota error:",e),e}}static async changePassword(e){try{return await this.withTokenRefresh(async()=>await s.uE.post(s.Sn.AUTH.CHANGE_PASSWORD,e,!0))}catch(e){throw console.error("Change password error:",e),e}}static async forgotPassword(e){try{return await s.uE.post(s.Sn.AUTH.FORGOT_PASSWORD,e)}catch(e){throw console.error("Forgot password error:",e),e}}static async resetPassword(e){try{return await s.uE.post(s.Sn.AUTH.RESET_PASSWORD,e)}catch(e){throw console.error("Reset password error:",e),e}}static isLoggedIn(){return s.tC.isLoggedIn()}static getCurrentUserEmail(){return s.tC.getUserEmail()}static isAuthError(e){return e.code?"TOKEN_EXPIRED"===e.code||"TOKEN_INVALID"===e.code||"TOKEN_TYPE_INVALID"===e.code||"NO_TOKEN"===e.code:!!e.message&&(e.message.includes("401")||e.message.toLowerCase().includes("token")||e.message.toLowerCase().includes("expired")||e.message.includes("登录")||e.message.includes("unauthorized"))}static async withTokenRefresh(e){try{return await e()}catch(t){if(this.isAuthError(t))try{return await this.refreshToken(),await e()}catch(t){this.logout();let e=Error("Authentication failed - refresh token expired");throw e.code="REFRESH_TOKEN_EXPIRED",e.shouldRedirect=!0,e}throw t}}}class o{static async useCard(e){try{return await a.withTokenRefresh(async()=>await s.uE.post(s.Sn.CARD.USE,{code:e},!0))}catch(e){throw console.error("Use card error:",e),e}}}class i{static async generateSpeech(e){try{return await a.withTokenRefresh(async()=>{let t=await fetch(`${s.uE.baseURL}${s.Sn.TTS.GENERATE}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s.tC.getAccessToken()}`},body:JSON.stringify(e)});if(!t.ok){let e=await t.json().catch(()=>({})),r=Error(e.error||`HTTP ${t.status}: ${t.statusText}`);throw e.type&&(r.type=e.type),r}return await t.arrayBuffer()})}catch(e){throw console.error("Generate speech error:",e),e}}static async startAsyncGeneration(e){try{return await a.withTokenRefresh(async()=>{let t=await fetch(`${s.uE.baseURL}${s.Sn.TTS.GENERATE}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${s.tC.getAccessToken()}`},body:JSON.stringify(e)});if(!t.ok){let e=await t.json().catch(()=>({})),r=Error(e.error||`HTTP ${t.status}: ${t.statusText}`);throw e.type&&(r.type=e.type),r}return await t.json()})}catch(e){throw console.error("Start async generation error:",e),e}}static async checkTaskStatus(e){try{return await a.withTokenRefresh(async()=>{let t=await fetch(`${s.uE.baseURL}${s.Sn.TTS.STATUS}/${e}`,{method:"GET",headers:{Authorization:`Bearer ${s.tC.getAccessToken()}`}});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.error||`HTTP ${t.status}: ${t.statusText}`)}return await t.json()})}catch(e){throw console.error("Check task status error:",e),e}}static async downloadAudio(e){try{return await a.withTokenRefresh(async()=>{let t=await fetch(`${s.uE.baseURL}${s.Sn.TTS.DOWNLOAD}/${e}`,{method:"GET",headers:{Authorization:`Bearer ${s.tC.getAccessToken()}`}});if(!t.ok){let e=await t.json().catch(()=>({}));throw Error(e.error||`HTTP ${t.status}: ${t.statusText}`)}return await t.arrayBuffer()})}catch(e){throw console.error("Download audio error:",e),e}}static async downloadFromDirectUrl(e){try{let t="localhost"===window.location.hostname||"127.0.0.1"===window.location.hostname,r={method:"GET"};if(t)try{await fetch(e,{method:"HEAD",mode:"cors"})}catch(e){if(r.mode="no-cors",e instanceof TypeError&&e.message.includes("CORS"))throw Error(`CORS_ERROR: ${e.message}`)}let s=await fetch(e,r);if(!s.ok)throw Error(`R2 direct download failed: HTTP ${s.status}: ${s.statusText}`);return await s.arrayBuffer()}catch(e){if(e instanceof Error&&e.message.includes("CORS"))throw Error(`R2_CORS_ERROR: CORS configuration needed for r2-assets.aispeak.top. ${e.message}`);throw e}}static{this.downloadCache=new Map}static async pollTaskUntilComplete(e,t,r=60,s=2e3){if(this.downloadCache.has(e))return await this.downloadCache.get(e);let a=this.performPolling(e,t,r,s);this.downloadCache.set(e,a);try{let t=await a;return this.downloadCache.delete(e),t}catch(t){throw this.downloadCache.delete(e),t}}static async performPolling(e,t,r=60,s=2e3){let a=0,o=s;for(;a<r;)try{let r=await this.checkTaskStatus(e);if(t&&t(r),"complete"===r.status){if(!(r.audioUrl&&r.audioUrl.includes("r2-assets.aispeak.top")))return await this.downloadAudio(e);try{return await this.downloadFromDirectUrl(r.audioUrl)}catch(t){return await this.downloadAudio(e)}}else if("failed"===r.status)throw Error(r.error||"Task failed");await new Promise(e=>setTimeout(e,o)),o=Math.min(1.2*o,1e4),a++}catch(e){if(console.error(`Polling attempt ${a+1} failed:`,e),++a<r)await new Promise(e=>setTimeout(e,o)),o=Math.min(1.5*o,1e4);else throw e}throw Error("Task polling timeout - maximum attempts reached")}}class n{static async processText(e,t="auto"){try{return await a.withTokenRefresh(async()=>await s.uE.post(s.Sn.AUTO_TAG.PROCESS,{text:e,language:t},!0))}catch(e){throw console.error("Auto tag process error:",e),e}}static async getStatus(){try{return await a.withTokenRefresh(async()=>await s.uE.get(s.Sn.AUTO_TAG.STATUS,!0))}catch(e){throw console.error("Auto tag status error:",e),e}}}let c=a,d=o,u=n},4934:(e,t,r)=>{"use strict";r.d(t,{$:()=>d});var s=r(687),a=r(3210),o=r(8730),i=r(4224),n=r(6241);let c=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=a.forwardRef(({className:e,variant:t,size:r,asChild:a=!1,...i},d)=>{let u=a?o.DX:"button";return(0,s.jsx)(u,{className:(0,n.cn)(c({variant:t,size:r,className:e})),ref:d,...i})});d.displayName="Button"},5192:(e,t,r)=>{"use strict";r.d(t,{Wu:()=>d,ZB:()=>c,Zp:()=>i,aR:()=>n});var s=r(687),a=r(3210),o=r(6241);let i=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let n=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("flex flex-col space-y-1.5 p-6",e),...t}));n.displayName="CardHeader";let c=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));c.displayName="CardTitle",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("text-sm text-muted-foreground",e),...t})).displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>(0,s.jsx)("div",{ref:r,className:(0,o.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},6241:(e,t,r)=>{"use strict";r.d(t,{cn:()=>o});var s=r(9384),a=r(2348);function o(...e){return(0,a.QP)((0,s.$)(e))}},6840:(e,t,r)=>{Promise.resolve().then(r.bind(r,2358))},8014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i,metadata:()=>o});var s=r(7413);r(2704);var a=r(2358);let o={title:"AI Voice Generator",description:"Created with v0",generator:"v0.dev",icons:{icon:"https://img.icons8.com/color/48/audiomack.png"}};function i({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsxs)("body",{children:[e,(0,s.jsx)(a.Toaster,{})]})})}},9556:(e,t,r)=>{"use strict";r.d(t,{Sn:()=>a,tC:()=>o,uE:()=>n});let s={BASE_URL:"https://my.aispeak.top",BACKUP_URL:"https://my.aispeak.top",BACKUP_URLS:"https://my.aispeak.top".split(",").map(e=>e.trim()).filter(e=>e.length>0),ENABLE_BACKUP:!0,TIMEOUT:3e4},a={AUTH:{LOGIN:"/api/auth/login",REGISTER:"/api/auth/register",REFRESH:"/api/auth/refresh",SEND_VERIFICATION:"/api/auth/send-verification",VERIFY_EMAIL:"/api/auth/verify-email",CHANGE_PASSWORD:"/api/auth/change-password",FORGOT_PASSWORD:"/api/auth/forgot-password",RESET_PASSWORD:"/api/auth/reset-password"},TTS:{GENERATE:"/api/tts/generate",STATUS:"/api/tts/status",STREAM:"/api/tts/stream",DOWNLOAD:"/api/tts/download"},USER:{QUOTA:"/api/user/quota"},CARD:{USE:"/api/card/use"},AUTO_TAG:{PROCESS:"/api/auto-tag/process",STATUS:"/api/auto-tag/status",ADMIN_STATS:"/api/auto-tag/admin/stats"}};class o{static{this.ACCESS_TOKEN_KEY="access_token"}static{this.REFRESH_TOKEN_KEY="refresh_token"}static{this.USER_EMAIL_KEY="userEmail"}static getAccessToken(){return null}static getRefreshToken(){return null}static getUserEmail(){return null}static setTokens(e,t,r){}static clearTokens(){}static isLoggedIn(){return!!this.getAccessToken()}}class i{constructor(){this.baseURL=s.BASE_URL,this.backupURL=s.BACKUP_URL,this.backupURLs=s.BACKUP_URLS,this.enableBackup=s.ENABLE_BACKUP,this.timeout=s.TIMEOUT}getCurrentApiUrl(e=!1,t=0){if(e&&this.enableBackup){if(this.backupURLs.length>0&&t>=0&&t<this.backupURLs.length)return this.backupURLs[t];if(this.backupURL)return this.backupURL}return this.baseURL}isBackupApiAvailable(){return this.enableBackup&&(this.backupURLs.length>0||!!this.backupURL)}getBackupApiCount(){return this.enableBackup?this.backupURLs.length>0?this.backupURLs.length:+!!this.backupURL:0}getBackupApiUrl(e){return this.enableBackup?this.backupURLs.length>0?e>=0&&e<this.backupURLs.length?this.backupURLs[e]:null:0===e&&this.backupURL?this.backupURL:null:null}createHeaders(e=!1){let t={"Content-Type":"application/json"};if(e){let e=o.getAccessToken();e&&(t.Authorization=`Bearer ${e}`)}return t}async handleResponse(e){if(!e.ok){let t=`HTTP ${e.status}: ${e.statusText}`,r=null;try{let s=await e.json();t=s.error||s.message||t,r=s.code||null}catch{}let s=Error(t);throw r&&(s.code=r),s}return await e.json()}async request(e,t={},r=!1){let s=`${this.baseURL}${e}`,a=this.createHeaders(r),o={...t,headers:{...a,...t.headers}},i=new AbortController,n=setTimeout(()=>i.abort(),this.timeout);try{let e=await fetch(s,{...o,signal:i.signal});return clearTimeout(n),await this.handleResponse(e)}catch(e){if(clearTimeout(n),e instanceof Error){if("AbortError"===e.name)throw Error("请求超时，请检查网络连接");throw e}throw Error("网络请求失败")}}async get(e,t=!1){return this.request(e,{method:"GET"},t)}async post(e,t,r=!1){return this.request(e,{method:"POST",body:t?JSON.stringify(t):void 0},r)}async put(e,t,r=!1){return this.request(e,{method:"PUT",body:t?JSON.stringify(t):void 0},r)}async delete(e,t=!1){return this.request(e,{method:"DELETE"},t)}}let n=new i},9794:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>T});var s=r(687),a=r(1702),o=r(3210),i=r(8810),n=r(4224),c=r(1860),d=r(6241);let u=i.Kq,l=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.LM,{ref:r,className:(0,d.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));l.displayName=i.LM.displayName;let h=(0,n.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),p=o.forwardRef(({className:e,variant:t,...r},a)=>(0,s.jsx)(i.bL,{ref:a,className:(0,d.cn)(h({variant:t}),e),...r}));p.displayName=i.bL.displayName,o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.rc,{ref:r,className:(0,d.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=i.rc.displayName;let f=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.bm,{ref:r,className:(0,d.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:(0,s.jsx)(c.A,{className:"h-4 w-4"})}));f.displayName=i.bm.displayName;let m=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.hE,{ref:r,className:(0,d.cn)("text-sm font-semibold",e),...t}));m.displayName=i.hE.displayName;let w=o.forwardRef(({className:e,...t},r)=>(0,s.jsx)(i.VY,{ref:r,className:(0,d.cn)("text-sm opacity-90",e),...t}));function T(){let{toasts:e}=(0,a.dj)();return(0,s.jsxs)(u,{children:[e.map(function({id:e,title:t,description:r,action:a,...o}){return(0,s.jsxs)(p,{...o,children:[(0,s.jsxs)("div",{className:"grid gap-1",children:[t&&(0,s.jsx)(m,{children:t}),r&&(0,s.jsx)(w,{children:r})]}),a,(0,s.jsx)(f,{})]},e)}),(0,s.jsx)(l,{})]})}w.displayName=i.VY.displayName}};