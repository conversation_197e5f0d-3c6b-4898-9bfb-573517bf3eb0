(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{2284:(e,t,r)=>{"use strict";r.d(t,{N:()=>l});var n=r(2115),o=r(6081),s=r(6101),a=r(9708),i=r(5155);function l(e){let t=e+"CollectionProvider",[r,l]=(0,o.A)(t),[u,d]=r(t,{collectionRef:{current:null},itemMap:new Map}),c=e=>{let{scope:t,children:r}=e,o=n.useRef(null),s=n.useRef(new Map).current;return(0,i.jsx)(u,{scope:t,itemMap:s,collectionRef:o,children:r})};c.displayName=t;let f=e+"CollectionSlot",p=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,o=d(f,r),l=(0,s.s)(t,o.collectionRef);return(0,i.jsx)(a.DX,{ref:l,children:n})});p.displayName=f;let v=e+"CollectionItemSlot",m="data-radix-collection-item",w=n.forwardRef((e,t)=>{let{scope:r,children:o,...l}=e,u=n.useRef(null),c=(0,s.s)(t,u),f=d(v,r);return n.useEffect(()=>(f.itemMap.set(u,{ref:u,...l}),()=>void f.itemMap.delete(u))),(0,i.jsx)(a.DX,{[m]:"",ref:c,children:o})});return w.displayName=v,[{Provider:c,Slot:p,ItemSlot:w},function(t){let r=d(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},l]}},3580:(e,t,r)=>{"use strict";r.d(t,{dj:()=>f});var n=r(2115);let o=0,s=new Map,a=e=>{if(s.has(e))return;let t=setTimeout(()=>{s.delete(e),d({type:"REMOVE_TOAST",toastId:e})},1e6);s.set(e,t)},i=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?a(r):e.toasts.forEach(e=>{a(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],u={toasts:[]};function d(e){u=i(u,e),l.forEach(e=>{e(u)})}function c(e){let{...t}=e,r=(o=(o+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>d({type:"DISMISS_TOAST",toastId:r});return d({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>d({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function f(){let[e,t]=n.useState(u);return n.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:c,dismiss:e=>d({type:"DISMISS_TOAST",toastId:e})}}},3654:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,9324,23)),Promise.resolve().then(r.bind(r,7216))},3999:(e,t,r)=>{"use strict";r.d(t,{cn:()=>s});var n=r(2596),o=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,n.$)(t))}},4378:(e,t,r)=>{"use strict";r.d(t,{Z:()=>l});var n=r(2115),o=r(7650),s=r(3655),a=r(2712),i=r(5155),l=n.forwardRef((e,t)=>{var r,l;let{container:u,...d}=e,[c,f]=n.useState(!1);(0,a.N)(()=>f(!0),[]);let p=u||c&&(null===(l=globalThis)||void 0===l?void 0:null===(r=l.document)||void 0===r?void 0:r.body);return p?o.createPortal((0,i.jsx)(s.sG.div,{...d,ref:t}),p):null});l.displayName="Portal"},4416:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});let n=(0,r(9946).A)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},7216:(e,t,r)=>{"use strict";r.d(t,{Toaster:()=>ea});var n=r(5155),o=r(3580),s=r(2115),a=r(7650),i=r(5185),l=r(6101),u=r(2284),d=r(6081),c=r(9178),f=r(4378),p=r(8905),v=r(3655),m=r(9033),w=r(5845),y=r(2712),x=s.forwardRef((e,t)=>(0,n.jsx)(v.sG.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));x.displayName="VisuallyHidden";var E="ToastProvider",[h,g,b]=(0,u.N)("Toast"),[T,C]=(0,d.A)("Toast",[b]),[R,P]=T(E),N=e=>{let{__scopeToast:t,label:r="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:i=50,children:l}=e,[u,d]=s.useState(null),[c,f]=s.useState(0),p=s.useRef(!1),v=s.useRef(!1);return r.trim()||console.error("Invalid prop `label` supplied to `".concat(E,"`. Expected non-empty `string`.")),(0,n.jsx)(h.Provider,{scope:t,children:(0,n.jsx)(R,{scope:t,label:r,duration:o,swipeDirection:a,swipeThreshold:i,toastCount:c,viewport:u,onViewportChange:d,onToastAdd:s.useCallback(()=>f(e=>e+1),[]),onToastRemove:s.useCallback(()=>f(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:v,children:l})})};N.displayName=E;var S="ToastViewport",j=["F8"],D="toast.viewportPause",L="toast.viewportResume",A=s.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:o=j,label:a="Notifications ({hotkey})",...i}=e,u=P(S,r),d=g(r),f=s.useRef(null),p=s.useRef(null),m=s.useRef(null),w=s.useRef(null),y=(0,l.s)(t,w,u.onViewportChange),x=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),E=u.toastCount>0;s.useEffect(()=>{let e=e=>{var t;0!==o.length&&o.every(t=>e[t]||e.code===t)&&(null===(t=w.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[o]),s.useEffect(()=>{let e=f.current,t=w.current;if(E&&e&&t){let r=()=>{if(!u.isClosePausedRef.current){let e=new CustomEvent(D);t.dispatchEvent(e),u.isClosePausedRef.current=!0}},n=()=>{if(u.isClosePausedRef.current){let e=new CustomEvent(L);t.dispatchEvent(e),u.isClosePausedRef.current=!1}},o=t=>{e.contains(t.relatedTarget)||n()},s=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",o),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",s),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",s),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[E,u.isClosePausedRef]);let b=s.useCallback(e=>{let{tabbingDirection:t}=e,r=d().map(e=>{let r=e.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?n:n.reverse()});return("forwards"===t?r.reverse():r).flat()},[d]);return s.useEffect(()=>{let e=w.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var n,o,s;let r=document.activeElement,a=t.shiftKey;if(t.target===e&&a){null===(n=p.current)||void 0===n||n.focus();return}let i=b({tabbingDirection:a?"backwards":"forwards"}),l=i.findIndex(e=>e===r);Z(i.slice(l+1))?t.preventDefault():a?null===(o=p.current)||void 0===o||o.focus():null===(s=m.current)||void 0===s||s.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[d,b]),(0,n.jsxs)(c.lg,{ref:f,role:"region","aria-label":a.replace("{hotkey}",x),tabIndex:-1,style:{pointerEvents:E?void 0:"none"},children:[E&&(0,n.jsx)(O,{ref:p,onFocusFromOutsideViewport:()=>{Z(b({tabbingDirection:"forwards"}))}}),(0,n.jsx)(h.Slot,{scope:r,children:(0,n.jsx)(v.sG.ol,{tabIndex:-1,...i,ref:y})}),E&&(0,n.jsx)(O,{ref:m,onFocusFromOutsideViewport:()=>{Z(b({tabbingDirection:"backwards"}))}})]})});A.displayName=S;var k="ToastFocusProxy",O=s.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:o,...s}=e,a=P(k,r);return(0,n.jsx)(x,{"aria-hidden":!0,tabIndex:0,...s,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null===(t=a.viewport)||void 0===t?void 0:t.contains(r))||o()}})});O.displayName=k;var I="Toast",F=s.forwardRef((e,t)=>{let{forceMount:r,open:o,defaultOpen:s,onOpenChange:a,...l}=e,[u=!0,d]=(0,w.i)({prop:o,defaultProp:s,onChange:a});return(0,n.jsx)(p.C,{present:r||u,children:(0,n.jsx)(K,{open:u,...l,ref:t,onClose:()=>d(!1),onPause:(0,m.c)(e.onPause),onResume:(0,m.c)(e.onResume),onSwipeStart:(0,i.m)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,i.m)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,i.m)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,i.m)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),d(!1)})})})});F.displayName=I;var[M,_]=T(I,{onClose(){}}),K=s.forwardRef((e,t)=>{let{__scopeToast:r,type:o="foreground",duration:u,open:d,onClose:f,onEscapeKeyDown:p,onPause:w,onResume:y,onSwipeStart:x,onSwipeMove:E,onSwipeCancel:g,onSwipeEnd:b,...T}=e,C=P(I,r),[R,N]=s.useState(null),S=(0,l.s)(t,e=>N(e)),j=s.useRef(null),A=s.useRef(null),k=u||C.duration,O=s.useRef(0),F=s.useRef(k),_=s.useRef(0),{onToastAdd:K,onToastRemove:W}=C,V=(0,m.c)(()=>{var e;(null==R?void 0:R.contains(document.activeElement))&&(null===(e=C.viewport)||void 0===e||e.focus()),f()}),X=s.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(_.current),O.current=new Date().getTime(),_.current=window.setTimeout(V,e))},[V]);s.useEffect(()=>{let e=C.viewport;if(e){let t=()=>{X(F.current),null==y||y()},r=()=>{let e=new Date().getTime()-O.current;F.current=F.current-e,window.clearTimeout(_.current),null==w||w()};return e.addEventListener(D,r),e.addEventListener(L,t),()=>{e.removeEventListener(D,r),e.removeEventListener(L,t)}}},[C.viewport,k,w,y,X]),s.useEffect(()=>{d&&!C.isClosePausedRef.current&&X(k)},[d,k,C.isClosePausedRef,X]),s.useEffect(()=>(K(),()=>W()),[K,W]);let z=s.useMemo(()=>R?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var n;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(n=t).nodeType===n.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,o=""===t.dataset.radixToastAnnounceExclude;if(!n){if(o){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(R):null,[R]);return C.viewport?(0,n.jsxs)(n.Fragment,{children:[z&&(0,n.jsx)(G,{__scopeToast:r,role:"status","aria-live":"foreground"===o?"assertive":"polite","aria-atomic":!0,children:z}),(0,n.jsx)(M,{scope:r,onClose:V,children:a.createPortal((0,n.jsx)(h.ItemSlot,{scope:r,children:(0,n.jsx)(c.bL,{asChild:!0,onEscapeKeyDown:(0,i.m)(p,()=>{C.isFocusedToastEscapeKeyDownRef.current||V(),C.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,n.jsx)(v.sG.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":d?"open":"closed","data-swipe-direction":C.swipeDirection,...T,ref:S,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,i.m)(e.onKeyDown,e=>{"Escape"!==e.key||(null==p||p(e.nativeEvent),e.nativeEvent.defaultPrevented||(C.isFocusedToastEscapeKeyDownRef.current=!0,V()))}),onPointerDown:(0,i.m)(e.onPointerDown,e=>{0===e.button&&(j.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,i.m)(e.onPointerMove,e=>{if(!j.current)return;let t=e.clientX-j.current.x,r=e.clientY-j.current.y,n=!!A.current,o=["left","right"].includes(C.swipeDirection),s=["left","up"].includes(C.swipeDirection)?Math.min:Math.max,a=o?s(0,t):0,i=o?0:s(0,r),l="touch"===e.pointerType?10:2,u={x:a,y:i},d={originalEvent:e,delta:u};n?(A.current=u,H("toast.swipeMove",E,d,{discrete:!1})):Y(u,C.swipeDirection,l)?(A.current=u,H("toast.swipeStart",x,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(j.current=null)}),onPointerUp:(0,i.m)(e.onPointerUp,e=>{let t=A.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),A.current=null,j.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};Y(t,C.swipeDirection,C.swipeThreshold)?H("toast.swipeEnd",b,n,{discrete:!0}):H("toast.swipeCancel",g,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),C.viewport)})]}):null}),G=e=>{let{__scopeToast:t,children:r,...o}=e,a=P(I,t),[i,l]=s.useState(!1),[u,d]=s.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,m.c)(e);(0,y.N)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>l(!0)),s.useEffect(()=>{let e=window.setTimeout(()=>d(!0),1e3);return()=>window.clearTimeout(e)},[]),u?null:(0,n.jsx)(f.Z,{asChild:!0,children:(0,n.jsx)(x,{...o,children:i&&(0,n.jsxs)(n.Fragment,{children:[a.label," ",r]})})})},W=s.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e;return(0,n.jsx)(v.sG.div,{...o,ref:t})});W.displayName="ToastTitle";var V=s.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e;return(0,n.jsx)(v.sG.div,{...o,ref:t})});V.displayName="ToastDescription";var X="ToastAction",z=s.forwardRef((e,t)=>{let{altText:r,...o}=e;return r.trim()?(0,n.jsx)(q,{altText:r,asChild:!0,children:(0,n.jsx)(U,{...o,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(X,"`. Expected non-empty `string`.")),null)});z.displayName=X;var B="ToastClose",U=s.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e,s=_(B,r);return(0,n.jsx)(q,{asChild:!0,children:(0,n.jsx)(v.sG.button,{type:"button",...o,ref:t,onClick:(0,i.m)(e.onClick,s.onClose)})})});U.displayName=B;var q=s.forwardRef((e,t)=>{let{__scopeToast:r,altText:o,...s}=e;return(0,n.jsx)(v.sG.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":o||void 0,...s,ref:t})});function H(e,t,r,n){let{discrete:o}=n,s=r.originalEvent.currentTarget,a=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),o?(0,v.hO)(s,a):s.dispatchEvent(a)}var Y=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,n=Math.abs(e.x),o=Math.abs(e.y),s=n>o;return"left"===t||"right"===t?s&&n>r:!s&&o>r};function Z(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var Q=r(2085),$=r(4416),J=r(3999);let ee=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(A,{ref:t,className:(0,J.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...o})});ee.displayName=A.displayName;let et=(0,Q.F)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),er=s.forwardRef((e,t)=>{let{className:r,variant:o,...s}=e;return(0,n.jsx)(F,{ref:t,className:(0,J.cn)(et({variant:o}),r),...s})});er.displayName=F.displayName,s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(z,{ref:t,className:(0,J.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...o})}).displayName=z.displayName;let en=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(U,{ref:t,className:(0,J.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...o,children:(0,n.jsx)($.A,{className:"h-4 w-4"})})});en.displayName=U.displayName;let eo=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(W,{ref:t,className:(0,J.cn)("text-sm font-semibold",r),...o})});eo.displayName=W.displayName;let es=s.forwardRef((e,t)=>{let{className:r,...o}=e;return(0,n.jsx)(V,{ref:t,className:(0,J.cn)("text-sm opacity-90",r),...o})});function ea(){let{toasts:e}=(0,o.dj)();return(0,n.jsxs)(N,{children:[e.map(function(e){let{id:t,title:r,description:o,action:s,...a}=e;return(0,n.jsxs)(er,{...a,children:[(0,n.jsxs)("div",{className:"grid gap-1",children:[r&&(0,n.jsx)(eo,{children:r}),o&&(0,n.jsx)(es,{children:o})]}),s,(0,n.jsx)(en,{})]},t)}),(0,n.jsx)(ee,{})]})}es.displayName=V.displayName},9178:(e,t,r)=>{"use strict";r.d(t,{lg:()=>y,qW:()=>f,bL:()=>w});var n,o=r(2115),s=r(5185),a=r(3655),i=r(6101),l=r(9033),u=r(5155),d="dismissableLayer.update",c=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),f=o.forwardRef((e,t)=>{var r,f;let{disableOutsidePointerEvents:p=!1,onEscapeKeyDown:w,onPointerDownOutside:y,onFocusOutside:x,onInteractOutside:E,onDismiss:h,...g}=e,b=o.useContext(c),[T,C]=o.useState(null),R=null!==(f=null==T?void 0:T.ownerDocument)&&void 0!==f?f:null===(r=globalThis)||void 0===r?void 0:r.document,[,P]=o.useState({}),N=(0,i.s)(t,e=>C(e)),S=Array.from(b.layers),[j]=[...b.layersWithOutsidePointerEventsDisabled].slice(-1),D=S.indexOf(j),L=T?S.indexOf(T):-1,A=b.layersWithOutsidePointerEventsDisabled.size>0,k=L>=D,O=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,l.c)(e),s=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!s.current){let t=function(){m("dismissableLayer.pointerDownOutside",n,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(r.removeEventListener("click",a.current),a.current=t,r.addEventListener("click",a.current,{once:!0})):t()}else r.removeEventListener("click",a.current);s.current=!1},t=window.setTimeout(()=>{r.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),r.removeEventListener("pointerdown",e),r.removeEventListener("click",a.current)}},[r,n]),{onPointerDownCapture:()=>s.current=!0}}(e=>{let t=e.target,r=[...b.branches].some(e=>e.contains(t));!k||r||(null==y||y(e),null==E||E(e),e.defaultPrevented||null==h||h())},R),I=function(e){var t;let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===(t=globalThis)||void 0===t?void 0:t.document,n=(0,l.c)(e),s=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!s.current&&m("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return r.addEventListener("focusin",e),()=>r.removeEventListener("focusin",e)},[r,n]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}(e=>{let t=e.target;[...b.branches].some(e=>e.contains(t))||(null==x||x(e),null==E||E(e),e.defaultPrevented||null==h||h())},R);return!function(e,t=globalThis?.document){let r=(0,l.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{L===b.layers.size-1&&(null==w||w(e),!e.defaultPrevented&&h&&(e.preventDefault(),h()))},R),o.useEffect(()=>{if(T)return p&&(0===b.layersWithOutsidePointerEventsDisabled.size&&(n=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),b.layersWithOutsidePointerEventsDisabled.add(T)),b.layers.add(T),v(),()=>{p&&1===b.layersWithOutsidePointerEventsDisabled.size&&(R.body.style.pointerEvents=n)}},[T,R,p,b]),o.useEffect(()=>()=>{T&&(b.layers.delete(T),b.layersWithOutsidePointerEventsDisabled.delete(T),v())},[T,b]),o.useEffect(()=>{let e=()=>P({});return document.addEventListener(d,e),()=>document.removeEventListener(d,e)},[]),(0,u.jsx)(a.sG.div,{...g,ref:N,style:{pointerEvents:A?k?"auto":"none":void 0,...e.style},onFocusCapture:(0,s.m)(e.onFocusCapture,I.onFocusCapture),onBlurCapture:(0,s.m)(e.onBlurCapture,I.onBlurCapture),onPointerDownCapture:(0,s.m)(e.onPointerDownCapture,O.onPointerDownCapture)})});f.displayName="DismissableLayer";var p=o.forwardRef((e,t)=>{let r=o.useContext(c),n=o.useRef(null),s=(0,i.s)(t,n);return o.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(a.sG.div,{...e,ref:s})});function v(){let e=new CustomEvent(d);document.dispatchEvent(e)}function m(e,t,r,n){let{discrete:o}=n,s=r.originalEvent.target,i=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),o?(0,a.hO)(s,i):s.dispatchEvent(i)}p.displayName="DismissableLayerBranch";var w=f,y=p},9324:()=>{}},e=>{var t=t=>e(e.s=t);e.O(0,[533,228,441,684,358],()=>t(3654)),_N_E=e.O()}]);