"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[651],{1285:(e,t,n)=>{n.d(t,{B:()=>u});var r,o=n(2115),a=n(2712),i=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),c=0;function u(e){let[t,n]=o.useState(i());return(0,a.N)(()=>{e||n(e=>e??String(c++))},[e]),e||(t?`radix-${t}`:"")}},3651:(e,t,n)=>{n.d(t,{bm:()=>e9,UC:()=>e8,VY:()=>e6,hJ:()=>e2,ZL:()=>e5,bL:()=>e0,hE:()=>e3,l9:()=>e1});var r,o=n(2115),a=n(5185),i=n(6101),c=n(6081),u=n(1285),l=n(5845),s=n(9178),d=n(3655),f=n(9033),p=n(5155),v="focusScope.autoFocusOnMount",m="focusScope.autoFocusOnUnmount",h={bubbles:!1,cancelable:!0},g=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:a,onUnmountAutoFocus:c,...u}=e,[l,s]=o.useState(null),g=(0,f.c)(a),C=(0,f.c)(c),R=o.useRef(null),x=(0,i.s)(t,e=>s(e)),S=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(S.paused||!l)return;let t=e.target;l.contains(t)?R.current=t:E(R.current,{select:!0})},t=function(e){if(S.paused||!l)return;let t=e.relatedTarget;null===t||l.contains(t)||E(R.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&E(l)});return l&&n.observe(l,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,l,S.paused]),o.useEffect(()=>{if(l){w.add(S);let e=document.activeElement;if(!l.contains(e)){let t=new CustomEvent(v,h);l.addEventListener(v,g),l.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(E(r,{select:t}),document.activeElement!==n)return}(y(l).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&E(l))}return()=>{l.removeEventListener(v,g),setTimeout(()=>{let t=new CustomEvent(m,h);l.addEventListener(m,C),l.dispatchEvent(t),t.defaultPrevented||E(null!=e?e:document.body,{select:!0}),l.removeEventListener(m,C),w.remove(S)},0)}}},[l,g,C,S]);let N=o.useCallback(e=>{if(!n&&!r||S.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,a]=function(e){let t=y(e);return[b(t,e),b(t.reverse(),e)]}(t);r&&a?e.shiftKey||o!==a?e.shiftKey&&o===r&&(e.preventDefault(),n&&E(a,{select:!0})):(e.preventDefault(),n&&E(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,S.paused]);return(0,p.jsx)(d.sG.div,{tabIndex:-1,...u,ref:x,onKeyDown:N})});function y(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function b(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function E(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}g.displayName="FocusScope";var w=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=C(e,t)).unshift(t)},remove(t){var n;null===(n=(e=C(e,t))[0])||void 0===n||n.resume()}}}();function C(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}var R=n(4378),x=n(8905),S=0;function N(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var A=function(){return(A=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function k(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var D=("function"==typeof SuppressedError&&SuppressedError,"right-scroll-bar-position"),M="width-before-scroll-bar";function I(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var j="undefined"!=typeof window?o.useLayoutEffect:o.useEffect,O=new WeakMap;function T(e){return e}var L=function(e){void 0===e&&(e={});var t,n,r,o,a=(t=null,void 0===n&&(n=T),r=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return r.length?r[r.length-1]:null},useMedium:function(e){var t=n(e,o);return r.push(t),function(){r=r.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(o=!0;r.length;){var t=r;r=[],t.forEach(e)}r={push:function(t){return e(t)},filter:function(){return r}}},assignMedium:function(e){o=!0;var t=[];if(r.length){var n=r;r=[],n.forEach(e),t=r}var a=function(){var n=t;t=[],n.forEach(e)},i=function(){return Promise.resolve().then(a)};i(),r={push:function(e){t.push(e),i()},filter:function(e){return t=t.filter(e),r}}}});return a.options=A({async:!0,ssr:!1},e),a}(),P=function(){},F=o.forwardRef(function(e,t){var n,r,a,i,c=o.useRef(null),u=o.useState({onScrollCapture:P,onWheelCapture:P,onTouchMoveCapture:P}),l=u[0],s=u[1],d=e.forwardProps,f=e.children,p=e.className,v=e.removeScrollBar,m=e.enabled,h=e.shards,g=e.sideCar,y=e.noRelative,b=e.noIsolation,E=e.inert,w=e.allowPinchZoom,C=e.as,R=e.gapMode,x=k(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),S=(n=[c,t],r=function(e){return n.forEach(function(t){return I(t,e)})},(a=(0,o.useState)(function(){return{value:null,callback:r,facade:{get current(){return a.value},set current(value){var e=a.value;e!==value&&(a.value=value,a.callback(value,e))}}}})[0]).callback=r,i=a.facade,j(function(){var e=O.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||I(e,null)}),r.forEach(function(e){t.has(e)||I(e,o)})}O.set(i,n)},[n]),i),N=A(A({},x),l);return o.createElement(o.Fragment,null,m&&o.createElement(g,{sideCar:L,removeScrollBar:v,shards:h,noRelative:y,noIsolation:b,inert:E,setCallbacks:s,allowPinchZoom:!!w,lockRef:c,gapMode:R}),d?o.cloneElement(o.Children.only(f),A(A({},N),{ref:S})):o.createElement(void 0===C?"div":C,A({},N,{className:p,ref:S}),f))});F.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},F.classNames={fullWidth:M,zeroRight:D};var _=function(e){var t=e.sideCar,n=k(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return o.createElement(r,A({},n))};_.isSideCarExport=!0;var W=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var a,i;(a=t).styleSheet?a.styleSheet.cssText=o:a.appendChild(document.createTextNode(o)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},B=function(){var e=W();return function(t,n){o.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},K=function(){var e=B();return function(t){return e(t.styles,t.dynamic),null}},X={left:0,top:0,right:0,gap:0},Y=function(e){return parseInt(e||"",10)||0},G=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[Y(n),Y(r),Y(o)]},Z=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return X;var t=G(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},q=K(),U="data-scroll-locked",H=function(e,t,n,r){var o=e.left,a=e.top,i=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(U,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(D," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(M," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(D," .").concat(D," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(M," .").concat(M," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(U,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},z=function(){var e=parseInt(document.body.getAttribute(U)||"0",10);return isFinite(e)?e:0},V=function(){o.useEffect(function(){return document.body.setAttribute(U,(z()+1).toString()),function(){var e=z()-1;e<=0?document.body.removeAttribute(U):document.body.setAttribute(U,e.toString())}},[])},J=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,a=void 0===r?"margin":r;V();var i=o.useMemo(function(){return Z(a)},[a]);return o.createElement(q,{styles:H(i,!t,a,n?"":"!important")})},$=!1;if("undefined"!=typeof window)try{var Q=Object.defineProperty({},"passive",{get:function(){return $=!0,!0}});window.addEventListener("test",Q,Q),window.removeEventListener("test",Q,Q)}catch(e){$=!1}var ee=!!$&&{passive:!1},et=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},en=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),er(e,r)){var o=eo(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},er=function(e,t){return"v"===e?et(t,"overflowY"):et(t,"overflowX")},eo=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},ea=function(e,t,n,r,o){var a,i=(a=window.getComputedStyle(t).direction,"h"===e&&"rtl"===a?-1:1),c=i*r,u=n.target,l=t.contains(u),s=!1,d=c>0,f=0,p=0;do{if(!u)break;var v=eo(e,u),m=v[0],h=v[1]-v[2]-i*m;(m||h)&&er(e,u)&&(f+=h,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!l&&u!==document.body||l&&(t.contains(u)||t===u));return d&&(o&&1>Math.abs(f)||!o&&c>f)?s=!0:!d&&(o&&1>Math.abs(p)||!o&&-c>p)&&(s=!0),s},ei=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},ec=function(e){return[e.deltaX,e.deltaY]},eu=function(e){return e&&"current"in e?e.current:e},el=0,es=[];let ed=(L.useMedium(function(e){var t=o.useRef([]),n=o.useRef([0,0]),r=o.useRef(),a=o.useState(el++)[0],i=o.useState(K)[0],c=o.useRef(e);o.useEffect(function(){c.current=e},[e]),o.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(eu),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(a))}),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(a))})}}},[e.inert,e.lockRef.current,e.shards]);var u=o.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!c.current.allowPinchZoom;var o,a=ei(e),i=n.current,u="deltaX"in e?e.deltaX:i[0]-a[0],l="deltaY"in e?e.deltaY:i[1]-a[1],s=e.target,d=Math.abs(u)>Math.abs(l)?"h":"v";if("touches"in e&&"h"===d&&"range"===s.type)return!1;var f=en(d,s);if(!f)return!0;if(f?o=d:(o="v"===d?"h":"v",f=en(d,s)),!f)return!1;if(!r.current&&"changedTouches"in e&&(u||l)&&(r.current=o),!o)return!0;var p=r.current||o;return ea(p,t,e,"h"===p?u:l,!0)},[]),l=o.useCallback(function(e){if(es.length&&es[es.length-1]===i){var n="deltaY"in e?ec(e):ei(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(c.current.shards||[]).map(eu).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!c.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=o.useCallback(function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(a),setTimeout(function(){t.current=t.current.filter(function(e){return e!==a})},1)},[]),d=o.useCallback(function(e){n.current=ei(e),r.current=void 0},[]),f=o.useCallback(function(t){s(t.type,ec(t),t.target,u(t,e.lockRef.current))},[]),p=o.useCallback(function(t){s(t.type,ei(t),t.target,u(t,e.lockRef.current))},[]);o.useEffect(function(){return es.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,ee),document.addEventListener("touchmove",l,ee),document.addEventListener("touchstart",d,ee),function(){es=es.filter(function(e){return e!==i}),document.removeEventListener("wheel",l,ee),document.removeEventListener("touchmove",l,ee),document.removeEventListener("touchstart",d,ee)}},[]);var v=e.removeScrollBar,m=e.inert;return o.createElement(o.Fragment,null,m?o.createElement(i,{styles:"\n  .block-interactivity-".concat(a," {pointer-events: none;}\n  .allow-interactivity-").concat(a," {pointer-events: all;}\n")}):null,v?o.createElement(J,{noRelative:e.noRelative,gapMode:e.gapMode}):null)}),_);var ef=o.forwardRef(function(e,t){return o.createElement(F,A({},e,{ref:t,sideCar:ed}))});ef.classNames=F.classNames;var ep=function(e){return"undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},ev=new WeakMap,em=new WeakMap,eh={},eg=0,ey=function(e){return e&&(e.host||ey(e.parentNode))},eb=function(e,t,n,r){var o=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=ey(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});eh[n]||(eh[n]=new WeakMap);var a=eh[n],i=[],c=new Set,u=new Set(o),l=function(e){!(!e||c.has(e))&&(c.add(e),l(e.parentNode))};o.forEach(l);var s=function(e){!(!e||u.has(e))&&Array.prototype.forEach.call(e.children,function(e){if(c.has(e))s(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,u=(ev.get(e)||0)+1,l=(a.get(e)||0)+1;ev.set(e,u),a.set(e,l),i.push(e),1===u&&o&&em.set(e,!0),1===l&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return s(t),c.clear(),eg++,function(){i.forEach(function(e){var t=ev.get(e)-1,o=a.get(e)-1;ev.set(e,t),a.set(e,o),t||(em.has(e)||e.removeAttribute(r),em.delete(e)),o||e.removeAttribute(n)}),--eg||(ev=new WeakMap,ev=new WeakMap,em=new WeakMap,eh={})}},eE=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||ep(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),eb(r,o,n,"aria-hidden")):function(){return null}},ew=n(9708),eC="Dialog",[eR,ex]=(0,c.A)(eC),[eS,eN]=eR(eC),eA=e=>{let{__scopeDialog:t,children:n,open:r,defaultOpen:a,onOpenChange:i,modal:c=!0}=e,s=o.useRef(null),d=o.useRef(null),[f=!1,v]=(0,l.i)({prop:r,defaultProp:a,onChange:i});return(0,p.jsx)(eS,{scope:t,triggerRef:s,contentRef:d,contentId:(0,u.B)(),titleId:(0,u.B)(),descriptionId:(0,u.B)(),open:f,onOpenChange:v,onOpenToggle:o.useCallback(()=>v(e=>!e),[v]),modal:c,children:n})};eA.displayName=eC;var ek="DialogTrigger",eD=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eN(ek,n),c=(0,i.s)(t,o.triggerRef);return(0,p.jsx)(d.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":eH(o.open),...r,ref:c,onClick:(0,a.m)(e.onClick,o.onOpenToggle)})});eD.displayName=ek;var eM="DialogPortal",[eI,ej]=eR(eM,{forceMount:void 0}),eO=e=>{let{__scopeDialog:t,forceMount:n,children:r,container:a}=e,i=eN(eM,t);return(0,p.jsx)(eI,{scope:t,forceMount:n,children:o.Children.map(r,e=>(0,p.jsx)(x.C,{present:n||i.open,children:(0,p.jsx)(R.Z,{asChild:!0,container:a,children:e})}))})};eO.displayName=eM;var eT="DialogOverlay",eL=o.forwardRef((e,t)=>{let n=ej(eT,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eN(eT,e.__scopeDialog);return a.modal?(0,p.jsx)(x.C,{present:r||a.open,children:(0,p.jsx)(eP,{...o,ref:t})}):null});eL.displayName=eT;var eP=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eN(eT,n);return(0,p.jsx)(ef,{as:ew.DX,allowPinchZoom:!0,shards:[o.contentRef],children:(0,p.jsx)(d.sG.div,{"data-state":eH(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),eF="DialogContent",e_=o.forwardRef((e,t)=>{let n=ej(eF,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,a=eN(eF,e.__scopeDialog);return(0,p.jsx)(x.C,{present:r||a.open,children:a.modal?(0,p.jsx)(eW,{...o,ref:t}):(0,p.jsx)(eB,{...o,ref:t})})});e_.displayName=eF;var eW=o.forwardRef((e,t)=>{let n=eN(eF,e.__scopeDialog),r=o.useRef(null),c=(0,i.s)(t,n.contentRef,r);return o.useEffect(()=>{let e=r.current;if(e)return eE(e)},[]),(0,p.jsx)(eK,{...e,ref:c,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()}),onPointerDownOutside:(0,a.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,a.m)(e.onFocusOutside,e=>e.preventDefault())})}),eB=o.forwardRef((e,t)=>{let n=eN(eF,e.__scopeDialog),r=o.useRef(!1),a=o.useRef(!1);return(0,p.jsx)(eK,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var o,i;null===(o=e.onCloseAutoFocus)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current||null===(i=n.triggerRef.current)||void 0===i||i.focus(),t.preventDefault()),r.current=!1,a.current=!1},onInteractOutside:t=>{var o,i;null===(o=e.onInteractOutside)||void 0===o||o.call(e,t),t.defaultPrevented||(r.current=!0,"pointerdown"!==t.detail.originalEvent.type||(a.current=!0));let c=t.target;(null===(i=n.triggerRef.current)||void 0===i?void 0:i.contains(c))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),eK=o.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:c,...u}=e,l=eN(eF,n),d=o.useRef(null),f=(0,i.s)(t,d);return o.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:N()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:N()),S++,()=>{1===S&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),S--}},[]),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(g,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:c,children:(0,p.jsx)(s.qW,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":eH(l.open),...u,ref:f,onDismiss:()=>l.onOpenChange(!1)})}),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(e$,{titleId:l.titleId}),(0,p.jsx)(eQ,{contentRef:d,descriptionId:l.descriptionId})]})]})}),eX="DialogTitle",eY=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eN(eX,n);return(0,p.jsx)(d.sG.h2,{id:o.titleId,...r,ref:t})});eY.displayName=eX;var eG="DialogDescription",eZ=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eN(eG,n);return(0,p.jsx)(d.sG.p,{id:o.descriptionId,...r,ref:t})});eZ.displayName=eG;var eq="DialogClose",eU=o.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=eN(eq,n);return(0,p.jsx)(d.sG.button,{type:"button",...r,ref:t,onClick:(0,a.m)(e.onClick,()=>o.onOpenChange(!1))})});function eH(e){return e?"open":"closed"}eU.displayName=eq;var ez="DialogTitleWarning",[eV,eJ]=(0,c.q)(ez,{contentName:eF,titleName:eX,docsSlug:"dialog"}),e$=e=>{let{titleId:t}=e,n=eJ(ez),r="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return o.useEffect(()=>{t&&!document.getElementById(t)&&console.error(r)},[r,t]),null},eQ=e=>{let{contentRef:t,descriptionId:n}=e,r=eJ("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(r.contentName,"}.");return o.useEffect(()=>{var e;let r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");n&&r&&!document.getElementById(n)&&console.warn(a)},[a,t,n]),null},e0=eA,e1=eD,e5=eO,e2=eL,e8=e_,e3=eY,e6=eZ,e9=eU}}]);