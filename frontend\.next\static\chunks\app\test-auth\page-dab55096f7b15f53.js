(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[580],{3580:(e,t,s)=>{"use strict";s.d(t,{dj:()=>T});var i=s(2115);let r=0,a=new Map,o=e=>{if(a.has(e))return;let t=setTimeout(()=>{a.delete(e),c({type:"REMOVE_TOAST",toastId:e})},1e6);a.set(e,t)},n=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:s}=t;return s?o(s):e.toasts.forEach(e=>{o(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===s||void 0===s?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},l=[],d={toasts:[]};function c(e){d=n(d,e),l.forEach(e=>{e(d)})}function u(e){let{...t}=e,s=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),i=()=>c({type:"DISMISS_TOAST",toastId:s});return c({type:"ADD_TOAST",toast:{...t,id:s,open:!0,onOpenChange:e=>{e||i()}}}),{id:s,dismiss:i,update:e=>c({type:"UPDATE_TOAST",toast:{...e,id:s}})}}function T(){let[e,t]=i.useState(d);return i.useEffect(()=>(l.push(t),()=>{let e=l.indexOf(t);e>-1&&l.splice(e,1)}),[e]),{...e,toast:u,dismiss:e=>c({type:"DISMISS_TOAST",toastId:e})}}},7492:(e,t,s)=>{"use strict";s.d(t,{A:()=>o,tu:()=>a});let i={TOKEN_EXPIRED:"TOKEN_EXPIRED",TOKEN_INVALID:"TOKEN_INVALID",TOKEN_TYPE_INVALID:"TOKEN_TYPE_INVALID",NO_TOKEN:"NO_TOKEN",AUTH_ERROR:"AUTH_ERROR",REFRESH_TOKEN_EXPIRED:"REFRESH_TOKEN_EXPIRED"};function r(e){if(null==e?void 0:e.code)return Object.values(i).includes(e.code);if(null==e?void 0:e.message){let t=e.message.toLowerCase();return t.includes("token")||t.includes("expired")||t.includes("unauthorized")||t.includes("401")||t.includes("登录")||t.includes("refresh")}return!1}function a(e,t){let s=r(e);s&&t&&t();let i=r(e)?{title:"认证失败",description:"会话已过期，正在跳转到登录页面...",shouldRedirect:(null==e?void 0:e.shouldRedirect)===void 0||e.shouldRedirect}:{title:"操作失败",description:(null==e?void 0:e.message)||"请检查网络连接后重试",shouldRedirect:!1};return{isAuthError:s,message:i.description,shouldRedirect:i.shouldRedirect}}function o(e){var t;return r(e)?{statusDisplay:"请重新登录",toastTitle:"认证失败",toastDescription:"会话已过期，正在跳转到登录页面...",variant:"destructive"}:(null==e?void 0:null===(t=e.message)||void 0===t?void 0:t.includes("卡密"))?{statusDisplay:"充值失败",toastTitle:"充值失败",toastDescription:"卡密无效或已使用，请检查后重试",variant:"destructive"}:{statusDisplay:"获取失败",toastTitle:"操作失败",toastDescription:(null==e?void 0:e.message)||"请检查网络连接后重试",variant:"destructive"}}},9271:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var i=s(5155),r=s(2115),a=s(7168),o=s(8482),n=s(6194),l=s(1886),d=s(3580),c=s(7492);function u(){let[e,t]=(0,r.useState)(""),[s,u]=(0,r.useState)(!1),{toast:T}=(0,d.dj)(),E=async()=>{try{l.tC.setTokens("invalid_token","invalid_refresh_token","<EMAIL>");let e=await n.j2.getUserQuota();t("成功获取数据: "+JSON.stringify(e))}catch(i){console.error("测试错误:",i),t("捕获错误: "+i.message+" (code: "+(i.code||"无")+")");let{isAuthError:e,shouldRedirect:s}=(0,c.tu)(i,()=>{u(!0),T({title:"认证失败",description:"会话已过期，正在跳转到登录页面...",variant:"destructive"})});e&&s&&setTimeout(()=>{window.location.href="/login"},2e3)}},h=async()=>{try{l.tC.clearTokens();let e=await n.j2.getUserQuota();t("成功获取数据: "+JSON.stringify(e))}catch(i){console.error("测试错误:",i),t("捕获错误: "+i.message+" (code: "+(i.code||"无")+")");let{isAuthError:e,shouldRedirect:s}=(0,c.tu)(i,()=>{u(!0),T({title:"认证失败",description:"会话已过期，正在跳转到登录页面...",variant:"destructive"})});e&&s&&setTimeout(()=>{window.location.href="/login"},2e3)}};return(0,i.jsx)("div",{className:"container mx-auto p-4",children:(0,i.jsxs)(o.Zp,{className:"max-w-2xl mx-auto",children:[(0,i.jsx)(o.aR,{children:(0,i.jsx)(o.ZB,{children:"认证错误处理测试"})}),(0,i.jsxs)(o.Wu,{className:"space-y-4",children:[(0,i.jsxs)("div",{className:"space-y-2",children:[(0,i.jsx)(a.$,{onClick:E,variant:"outline",children:"测试无效Token"}),(0,i.jsx)(a.$,{onClick:h,variant:"outline",children:"测试无Token"})]}),e&&(0,i.jsxs)("div",{className:"p-4 bg-gray-100 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-semibold mb-2",children:"测试结果:"}),(0,i.jsx)("pre",{className:"text-sm whitespace-pre-wrap",children:e})]}),s&&(0,i.jsxs)("div",{className:"p-4 bg-red-100 border border-red-300 rounded-lg",children:[(0,i.jsx)("h3",{className:"font-semibold text-red-800",children:"认证错误弹窗已触发!"}),(0,i.jsx)("p",{className:"text-red-600",children:"正在准备跳转到登录页面..."})]})]})]})})}},9536:(e,t,s)=>{Promise.resolve().then(s.bind(s,9271))}},e=>{var t=t=>e(e.s=t);e.O(0,[352,576,441,684,358],()=>t(9536)),_N_E=e.O()}]);